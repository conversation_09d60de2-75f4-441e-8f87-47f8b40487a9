21:28:56.166 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 1005184 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
21:28:56.167 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
21:28:57.100 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
21:28:57.101 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
21:28:57.101 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
21:28:57.152 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
21:28:57.805 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
21:28:58.116 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
21:28:58.805 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
21:28:58.822 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 3.114 seconds (JVM running for 3.783)
21:29:19.006 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
21:33:36.328 [http-nio-9550-exec-6] INFO  c.l.w.c.WordExportController - [exportTable,38] - 接收到表格导出请求，标题: 检验记录表
21:33:36.328 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [exportTableToWord,34] - 开始导出Word文档，表格标题: 检验记录表
21:33:36.526 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [setPageOrientation,417] - 已设置文档为横向纸张
21:33:36.574 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [createTable,80] - 接收到表头数据，共2行
21:33:36.574 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [createTable,83] - 第1行表头，共8列
21:33:36.574 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [createTable,87] -   列0: content='检查工序名称', rowspan=2, colspan=1
21:33:36.574 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [createTable,87] -   列1: content='检查项目及技术条件', rowspan=2, colspan=1
21:33:36.574 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [createTable,87] -   列2: content='实际检查结果', rowspan=2, colspan=1
21:33:36.575 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [createTable,87] -   列3: content='完工', rowspan=1, colspan=2
21:33:36.575 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [createTable,87] -   列5: content='操作员', rowspan=2, colspan=1
21:33:36.575 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [createTable,87] -   列6: content='班组长', rowspan=2, colspan=1
21:33:36.575 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [createTable,87] -   列7: content='检验员', rowspan=2, colspan=1
21:33:36.575 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [createTable,83] - 第2行表头，共8列
21:33:36.575 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [createTable,87] -   列3: content='月', rowspan=1, colspan=1
21:33:36.575 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [createTable,87] -   列4: content='日', rowspan=1, colspan=1
21:33:36.575 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [createTable,107] - 创建表格: 3行 x 8列
21:33:36.611 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,152] - 开始处理表头，共2行
21:33:36.611 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,158] - 处理第1行表头，共8列
21:33:36.641 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,158] - 处理第2行表头，共8列
21:33:36.643 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,167] - 表头处理完成，共处理2行
21:33:36.656 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [createTable,141] - 表格创建完成
21:33:36.724 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [exportTableToWord,59] - Word文档导出完成，文件大小: 2881 bytes
21:33:36.730 [http-nio-9550-exec-6] INFO  c.l.w.c.WordExportController - [exportTable,52] - 表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250818_213336.docx, 大小: 2881 bytes
21:34:14.479 [http-nio-9550-exec-7] INFO  c.l.w.c.WordExportController - [exportTable,38] - 接收到表格导出请求，标题: 检验记录表
21:34:14.479 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [exportTableToWord,34] - 开始导出Word文档，表格标题: 检验记录表
21:34:14.481 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [setPageOrientation,417] - 已设置文档为横向纸张
21:34:14.482 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,80] - 接收到表头数据，共2行
21:34:14.483 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,83] - 第1行表头，共8列
21:34:14.483 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,87] -   列0: content='检查工序名称', rowspan=2, colspan=1
21:34:14.483 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,87] -   列1: content='检查项目及技术条件', rowspan=2, colspan=1
21:34:14.483 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,87] -   列2: content='实际检查结果', rowspan=2, colspan=1
21:34:14.483 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,87] -   列3: content='完工', rowspan=1, colspan=2
21:34:14.483 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,87] -   列5: content='操作员', rowspan=2, colspan=1
21:34:14.483 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,87] -   列6: content='班组长', rowspan=2, colspan=1
21:34:14.483 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,87] -   列7: content='检验员', rowspan=2, colspan=1
21:34:14.483 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,83] - 第2行表头，共8列
21:34:14.483 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,87] -   列3: content='月', rowspan=1, colspan=1
21:34:14.484 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,87] -   列4: content='日', rowspan=1, colspan=1
21:34:14.484 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,107] - 创建表格: 2行 x 8列
21:34:14.486 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,152] - 开始处理表头，共2行
21:34:14.486 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,158] - 处理第1行表头，共8列
21:34:14.492 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,158] - 处理第2行表头，共8列
21:34:14.493 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,167] - 表头处理完成，共处理2行
21:34:14.494 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,141] - 表格创建完成
21:34:14.500 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [exportTableToWord,59] - Word文档导出完成，文件大小: 2817 bytes
21:34:14.503 [http-nio-9550-exec-7] INFO  c.l.w.c.WordExportController - [exportTable,52] - 表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250818_213414.docx, 大小: 2817 bytes
21:36:58.003 [http-nio-9550-exec-9] INFO  c.l.w.c.WordExportController - [exportTable,38] - 接收到表格导出请求，标题: 检验记录表
21:36:58.004 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [exportTableToWord,34] - 开始导出Word文档，表格标题: 检验记录表
21:36:58.006 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [setPageOrientation,417] - 已设置文档为横向纸张
21:36:58.007 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [createTable,80] - 接收到表头数据，共2行
21:36:58.008 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [createTable,83] - 第1行表头，共8列
21:36:58.008 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [createTable,87] -   列0: content='检查工序名称', rowspan=2, colspan=1
21:36:58.008 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [createTable,87] -   列1: content='检查项目及技术条件', rowspan=2, colspan=1
21:36:58.008 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [createTable,87] -   列2: content='实际检查结果', rowspan=2, colspan=1
21:36:58.009 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [createTable,87] -   列3: content='完工', rowspan=1, colspan=2
21:36:58.009 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [createTable,87] -   列5: content='操作员', rowspan=2, colspan=1
21:36:58.009 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [createTable,87] -   列6: content='班组长', rowspan=2, colspan=1
21:36:58.009 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [createTable,87] -   列7: content='检验员', rowspan=2, colspan=1
21:36:58.009 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [createTable,83] - 第2行表头，共8列
21:36:58.010 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [createTable,87] -   列3: content='月', rowspan=1, colspan=1
21:36:58.010 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [createTable,87] -   列4: content='日', rowspan=1, colspan=1
21:36:58.010 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [createTable,107] - 创建表格: 2行 x 8列
21:36:58.011 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,152] - 开始处理表头，共2行
21:36:58.012 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,158] - 处理第1行表头，共8列
21:36:58.017 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,158] - 处理第2行表头，共8列
21:36:58.018 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,167] - 表头处理完成，共处理2行
21:36:58.019 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [createTable,141] - 表格创建完成
21:36:58.027 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [exportTableToWord,59] - Word文档导出完成，文件大小: 2817 bytes
21:36:58.034 [http-nio-9550-exec-9] INFO  c.l.w.c.WordExportController - [exportTable,52] - 表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250818_213658.docx, 大小: 2817 bytes
21:44:01.962 [http-nio-9550-exec-10] INFO  c.l.w.c.WordExportController - [exportTable,38] - 接收到表格导出请求，标题: 检验记录表
21:44:01.962 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [exportTableToWord,34] - 开始导出Word文档，表格标题: 检验记录表
21:44:01.963 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [setPageOrientation,417] - 已设置文档为横向纸张
21:44:01.964 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,80] - 接收到表头数据，共2行
21:44:01.964 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,83] - 第1行表头，共7列
21:44:01.964 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,87] -   列0: content='检查工序名称', rowspan=2, colspan=1
21:44:01.964 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,87] -   列1: content='检查项目及技术条件', rowspan=2, colspan=1
21:44:01.964 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,87] -   列2: content='实际检查结果', rowspan=2, colspan=1
21:44:01.964 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,87] -   列3: content='完工', rowspan=1, colspan=2
21:44:01.964 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,87] -   列4: content='操作员', rowspan=2, colspan=1
21:44:01.964 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,87] -   列5: content='班组长', rowspan=2, colspan=1
21:44:01.965 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,87] -   列6: content='检验员', rowspan=2, colspan=1
21:44:01.965 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,83] - 第2行表头，共8列
21:44:01.965 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,87] -   列3: content='月', rowspan=1, colspan=1
21:44:01.965 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,87] -   列4: content='日', rowspan=1, colspan=1
21:44:01.965 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,107] - 创建表格: 2行 x 8列
21:44:01.966 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,152] - 开始处理表头，共2行
21:44:01.966 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,158] - 处理第1行表头，共7列
21:44:01.970 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,158] - 处理第2行表头，共8列
21:44:01.971 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,167] - 表头处理完成，共处理2行
21:44:01.972 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,141] - 表格创建完成
21:44:01.976 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [exportTableToWord,59] - Word文档导出完成，文件大小: 2822 bytes
21:44:01.979 [http-nio-9550-exec-10] INFO  c.l.w.c.WordExportController - [exportTable,52] - 表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250818_214401.docx, 大小: 2822 bytes
21:44:05.327 [http-nio-9550-exec-3] INFO  c.l.w.c.WordExportController - [exportTable,38] - 接收到表格导出请求，标题: 检验记录表
21:44:05.328 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [exportTableToWord,34] - 开始导出Word文档，表格标题: 检验记录表
21:44:05.329 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [setPageOrientation,417] - 已设置文档为横向纸张
21:44:05.329 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [createTable,80] - 接收到表头数据，共2行
21:44:05.329 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [createTable,83] - 第1行表头，共7列
21:44:05.330 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [createTable,87] -   列0: content='检查工序名称', rowspan=2, colspan=1
21:44:05.330 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [createTable,87] -   列1: content='检查项目及技术条件', rowspan=2, colspan=1
21:44:05.330 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [createTable,87] -   列2: content='实际检查结果', rowspan=2, colspan=1
21:44:05.330 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [createTable,87] -   列3: content='完工', rowspan=1, colspan=2
21:44:05.330 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [createTable,87] -   列4: content='操作员', rowspan=2, colspan=1
21:44:05.330 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [createTable,87] -   列5: content='班组长', rowspan=2, colspan=1
21:44:05.330 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [createTable,87] -   列6: content='检验员', rowspan=2, colspan=1
21:44:05.330 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [createTable,83] - 第2行表头，共8列
21:44:05.330 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [createTable,87] -   列3: content='月', rowspan=1, colspan=1
21:44:05.330 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [createTable,87] -   列4: content='日', rowspan=1, colspan=1
21:44:05.331 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [createTable,107] - 创建表格: 2行 x 8列
21:44:05.331 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,152] - 开始处理表头，共2行
21:44:05.331 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,158] - 处理第1行表头，共7列
21:44:05.337 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,158] - 处理第2行表头，共8列
21:44:05.339 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,167] - 表头处理完成，共处理2行
21:44:05.340 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [createTable,141] - 表格创建完成
21:44:05.344 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [exportTableToWord,59] - Word文档导出完成，文件大小: 2823 bytes
21:44:05.347 [http-nio-9550-exec-3] INFO  c.l.w.c.WordExportController - [exportTable,52] - 表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250818_214405.docx, 大小: 2823 bytes
21:56:58.913 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
21:56:58.916 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
22:06:24.186 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 1052998 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
22:06:24.188 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
22:06:25.244 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
22:06:25.245 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
22:06:25.245 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
22:06:25.298 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
22:06:25.932 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
22:06:26.231 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
22:06:27.028 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
22:06:27.047 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 3.387 seconds (JVM running for 4.164)
22:06:34.463 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
22:06:34.496 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [testExport,105] - 开始测试Word导出...
22:06:34.497 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWord,34] - 开始导出Word文档，表格标题: 测试检验记录表
22:06:34.788 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,417] - 已设置文档为横向纸张
22:06:34.948 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,80] - 接收到表头数据，共2行
22:06:34.948 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,83] - 第1行表头，共7列
22:06:34.948 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,87] -   列0: content='检查工序名称', rowspan=2, colspan=1
22:06:34.948 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,87] -   列1: content='检查项目及技术条件', rowspan=2, colspan=1
22:06:34.948 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,87] -   列2: content='实际检查结果', rowspan=2, colspan=1
22:06:34.948 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,87] -   列3: content='完工', rowspan=1, colspan=2
22:06:34.949 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,87] -   列4: content='操作员', rowspan=2, colspan=1
22:06:34.949 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,87] -   列5: content='班组长', rowspan=2, colspan=1
22:06:34.949 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,87] -   列6: content='检验员', rowspan=2, colspan=1
22:06:34.949 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,83] - 第2行表头，共8列
22:06:34.949 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,87] -   列3: content='月', rowspan=1, colspan=1
22:06:34.949 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,87] -   列4: content='日', rowspan=1, colspan=1
22:06:34.949 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,107] - 创建表格: 5行 x 8列
22:06:34.994 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,152] - 开始处理表头，共2行
22:06:34.994 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,158] - 处理第1行表头，共7列
22:06:35.034 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,158] - 处理第2行表头，共8列
22:06:35.038 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,167] - 表头处理完成，共处理2行
22:06:35.064 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,141] - 表格创建完成
22:06:35.157 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWord,59] - Word文档导出完成，文件大小: 3104 bytes
22:06:35.173 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [testExport,122] - 测试导出成功，文件名: %E6%B5%8B%E8%AF%95%E5%AF%BC%E5%87%BA_20250818_220635.docx, 大小: 3104 bytes
22:13:16.058 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
22:13:16.060 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
22:13:20.608 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 1061865 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
22:13:20.609 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
22:13:21.651 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
22:13:21.652 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
22:13:21.652 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
22:13:21.705 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
22:13:22.324 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
22:13:22.607 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
22:13:23.350 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
22:13:23.369 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 3.204 seconds (JVM running for 3.897)
22:13:28.704 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
22:13:28.726 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [testExport,105] - 开始测试Word导出...
22:13:28.727 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWord,34] - 开始导出Word文档，表格标题: 测试检验记录表
22:13:28.986 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,431] - 已设置文档为横向纸张
22:13:29.142 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,80] - 接收到表头数据，共2行
22:13:29.143 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,83] - 第1行表头，共7列
22:13:29.143 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,87] -   列0: content='检查工序名称', rowspan=2, colspan=1
22:13:29.143 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,87] -   列1: content='检查项目及技术条件', rowspan=2, colspan=1
22:13:29.143 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,87] -   列2: content='实际检查结果', rowspan=2, colspan=1
22:13:29.143 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,87] -   列3: content='完工', rowspan=1, colspan=2
22:13:29.143 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,87] -   列4: content='操作员', rowspan=2, colspan=1
22:13:29.143 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,87] -   列5: content='班组长', rowspan=2, colspan=1
22:13:29.144 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,87] -   列6: content='检验员', rowspan=2, colspan=1
22:13:29.144 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,83] - 第2行表头，共8列
22:13:29.144 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,87] -   列3: content='月', rowspan=1, colspan=1
22:13:29.144 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,87] -   列4: content='日', rowspan=1, colspan=1
22:13:29.144 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,107] - 创建表格: 5行 x 8列
22:13:29.181 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,152] - 开始处理表头，共2行
22:13:29.182 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,158] - 处理第1行表头，共7列
22:13:29.220 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,158] - 处理第2行表头，共8列
22:13:29.224 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,167] - 表头处理完成，共处理2行
22:13:29.253 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,141] - 表格创建完成
22:13:29.361 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWord,59] - Word文档导出完成，文件大小: 3099 bytes
22:13:29.382 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [testExport,122] - 测试导出成功，文件名: %E6%B5%8B%E8%AF%95%E5%AF%BC%E5%87%BA_20250818_221329.docx, 大小: 3099 bytes
22:13:53.619 [http-nio-9550-exec-2] INFO  c.l.w.c.WordExportController - [testEmptyDataExport,139] - 开始测试空数据Word导出...
22:13:53.619 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [exportTableToWord,34] - 开始导出Word文档，表格标题: 空数据测试检验记录表
22:13:53.621 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [setPageOrientation,431] - 已设置文档为横向纸张
22:13:53.622 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,80] - 接收到表头数据，共2行
22:13:53.622 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,83] - 第1行表头，共7列
22:13:53.623 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,87] -   列0: content='检查工序名称', rowspan=2, colspan=1
22:13:53.623 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,87] -   列1: content='检查项目及技术条件', rowspan=2, colspan=1
22:13:53.623 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,87] -   列2: content='实际检查结果', rowspan=2, colspan=1
22:13:53.623 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,87] -   列3: content='完工', rowspan=1, colspan=2
22:13:53.623 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,87] -   列4: content='操作员', rowspan=2, colspan=1
22:13:53.623 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,87] -   列5: content='班组长', rowspan=2, colspan=1
22:13:53.623 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,87] -   列6: content='检验员', rowspan=2, colspan=1
22:13:53.624 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,83] - 第2行表头，共8列
22:13:53.624 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,87] -   列3: content='月', rowspan=1, colspan=1
22:13:53.624 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,87] -   列4: content='日', rowspan=1, colspan=1
22:13:53.624 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,107] - 创建表格: 2行 x 8列
22:13:53.626 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,152] - 开始处理表头，共2行
22:13:53.627 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,158] - 处理第1行表头，共7列
22:13:53.634 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,158] - 处理第2行表头，共8列
22:13:53.635 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,167] - 表头处理完成，共处理2行
22:13:53.636 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,141] - 表格创建完成
22:13:53.643 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [exportTableToWord,59] - Word文档导出完成，文件大小: 2833 bytes
22:13:53.647 [http-nio-9550-exec-2] INFO  c.l.w.c.WordExportController - [testEmptyDataExport,156] - 空数据测试导出成功，文件名: %E7%A9%BA%E6%95%B0%E6%8D%AE%E6%B5%8B%E8%AF%95%E5%AF%BC%E5%87%BA_20250818_221353.docx, 大小: 2833 bytes
22:30:55.469 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
22:30:55.471 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
22:31:00.757 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 1084611 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
22:31:00.760 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
22:31:02.011 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
22:31:02.012 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
22:31:02.012 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
22:31:02.074 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
22:31:02.825 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
22:31:03.169 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
22:31:04.197 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
22:31:04.218 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 4.092 seconds (JVM running for 4.906)
22:31:09.966 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
22:31:10.001 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [testEmptyDataExport,139] - 开始测试空数据Word导出...
22:31:10.003 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWord,34] - 开始导出Word文档，表格标题: 空数据测试检验记录表
22:31:10.326 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,431] - 已设置文档为横向纸张
22:31:10.520 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,80] - 接收到表头数据，共4行
22:31:10.520 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,83] - 第1行表头，共7列
22:31:10.520 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,87] -   列0: content='检查工序名称', rowspan=2, colspan=1
22:31:10.520 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,87] -   列1: content='检查项目及技术条件', rowspan=2, colspan=1
22:31:10.521 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,87] -   列2: content='实际检查结果', rowspan=2, colspan=1
22:31:10.521 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,87] -   列3: content='完工', rowspan=1, colspan=2
22:31:10.521 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,87] -   列4: content='操作员', rowspan=2, colspan=1
22:31:10.521 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,87] -   列5: content='班组长', rowspan=2, colspan=1
22:31:10.521 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,87] -   列6: content='检验员', rowspan=2, colspan=1
22:31:10.521 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,83] - 第2行表头，共8列
22:31:10.522 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,87] -   列3: content='月', rowspan=1, colspan=1
22:31:10.522 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,87] -   列4: content='日', rowspan=1, colspan=1
22:31:10.522 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,83] - 第3行表头，共1列
22:31:10.522 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,87] -   列0: content='对比：不合并表头', rowspan=1, colspan=8
22:31:10.522 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,83] - 第4行表头，共8列
22:31:10.522 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,87] -   列0: content='检查工序名称', rowspan=1, colspan=1
22:31:10.522 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,87] -   列1: content='检查项目及技术条件', rowspan=1, colspan=1
22:31:10.522 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,87] -   列2: content='实际检查结果', rowspan=1, colspan=1
22:31:10.523 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,87] -   列3: content='完工-月', rowspan=1, colspan=1
22:31:10.523 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,87] -   列4: content='完工-日', rowspan=1, colspan=1
22:31:10.523 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,87] -   列5: content='操作员', rowspan=1, colspan=1
22:31:10.523 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,87] -   列6: content='班组长', rowspan=1, colspan=1
22:31:10.523 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,87] -   列7: content='检验员', rowspan=1, colspan=1
22:31:10.523 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,107] - 创建表格: 4行 x 8列
22:31:10.580 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,152] - 开始处理表头，共4行
22:31:10.581 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,158] - 处理第1行表头，共7列
22:31:10.640 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,158] - 处理第2行表头，共8列
22:31:10.644 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,158] - 处理第3行表头，共1列
22:31:10.649 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,158] - 处理第4行表头，共8列
22:31:10.666 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,167] - 表头处理完成，共处理4行
22:31:10.676 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,141] - 表格创建完成
22:31:10.813 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWord,59] - Word文档导出完成，文件大小: 2941 bytes
22:31:10.842 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [testEmptyDataExport,156] - 空数据测试导出成功，文件名: %E7%A9%BA%E6%95%B0%E6%8D%AE%E6%B5%8B%E8%AF%95%E5%AF%BC%E5%87%BA_20250818_223110.docx, 大小: 2941 bytes
22:36:38.067 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
22:36:38.071 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
22:36:47.159 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 1092107 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
22:36:47.161 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
22:36:48.765 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
22:36:48.767 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
22:36:48.767 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
22:36:48.853 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
22:36:49.876 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
22:36:50.353 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
22:36:51.576 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
22:36:51.606 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 5.142 seconds (JVM running for 6.183)
22:37:25.488 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
22:37:25.534 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [testEmptyDataExport,139] - 开始测试空数据Word导出...
22:37:25.535 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWord,34] - 开始导出Word文档，表格标题: 空数据测试检验记录表
22:37:25.952 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,544] - 已设置文档为横向纸张
22:37:26.211 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createSingleTable,193] - 接收到表头数据，共2行
22:37:26.212 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createSingleTable,196] - 第1行表头，共7列
22:37:26.212 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createSingleTable,200] -   列0: content='检查工序名称', rowspan=2, colspan=1
22:37:26.213 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createSingleTable,200] -   列1: content='检查项目及技术条件', rowspan=2, colspan=1
22:37:26.213 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createSingleTable,200] -   列2: content='实际检查结果', rowspan=2, colspan=1
22:37:26.213 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createSingleTable,200] -   列3: content='完工', rowspan=1, colspan=2
22:37:26.213 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createSingleTable,200] -   列4: content='操作员', rowspan=2, colspan=1
22:37:26.213 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createSingleTable,200] -   列5: content='班组长', rowspan=2, colspan=1
22:37:26.214 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createSingleTable,200] -   列6: content='检验员', rowspan=2, colspan=1
22:37:26.214 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createSingleTable,196] - 第2行表头，共8列
22:37:26.214 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createSingleTable,200] -   列3: content='月', rowspan=1, colspan=1
22:37:26.214 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createSingleTable,200] -   列4: content='日', rowspan=1, colspan=1
22:37:26.214 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createSingleTable,220] - 创建表格: 2行 x 8列
22:37:26.278 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,265] - 开始处理表头，共2行
22:37:26.278 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,271] - 处理第1行表头，共7列
22:37:26.322 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,271] - 处理第2行表头，共8列
22:37:26.329 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,280] - 表头处理完成，共处理2行
22:37:26.340 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createSingleTable,254] - 单个表格创建完成
22:37:26.341 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createSingleTable,193] - 接收到表头数据，共2行
22:37:26.341 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createSingleTable,196] - 第1行表头，共8列
22:37:26.341 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createSingleTable,200] -   列0: content='检查工序名称', rowspan=1, colspan=1
22:37:26.342 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createSingleTable,200] -   列1: content='检查项目及技术条件', rowspan=1, colspan=1
22:37:26.342 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createSingleTable,200] -   列2: content='实际检查结果', rowspan=1, colspan=1
22:37:26.342 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createSingleTable,200] -   列3: content='完工-月', rowspan=1, colspan=1
22:37:26.342 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createSingleTable,200] -   列4: content='完工-日', rowspan=1, colspan=1
22:37:26.342 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createSingleTable,200] -   列5: content='操作员', rowspan=1, colspan=1
22:37:26.342 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createSingleTable,200] -   列6: content='班组长', rowspan=1, colspan=1
22:37:26.343 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createSingleTable,200] -   列7: content='检验员', rowspan=1, colspan=1
22:37:26.343 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createSingleTable,196] - 第2行表头，共8列
22:37:26.343 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createSingleTable,200] -   列0: content='工序名称2', rowspan=1, colspan=1
22:37:26.343 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createSingleTable,200] -   列1: content='技术条件2', rowspan=1, colspan=1
22:37:26.343 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createSingleTable,200] -   列2: content='检查结果2', rowspan=1, colspan=1
22:37:26.343 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createSingleTable,200] -   列3: content='月份2', rowspan=1, colspan=1
22:37:26.344 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createSingleTable,200] -   列4: content='日期2', rowspan=1, colspan=1
22:37:26.344 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createSingleTable,200] -   列5: content='操作员2', rowspan=1, colspan=1
22:37:26.344 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createSingleTable,200] -   列6: content='班组长2', rowspan=1, colspan=1
22:37:26.344 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createSingleTable,200] -   列7: content='检验员2', rowspan=1, colspan=1
22:37:26.344 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createSingleTable,220] - 创建表格: 2行 x 8列
22:37:26.349 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,265] - 开始处理表头，共2行
22:37:26.349 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,271] - 处理第1行表头，共8列
22:37:26.369 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,271] - 处理第2行表头，共8列
22:37:26.375 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,280] - 表头处理完成，共处理2行
22:37:26.377 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createSingleTable,254] - 单个表格创建完成
22:37:26.377 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createMultipleTables,179] - 多个表格创建完成，共2个表格
22:37:26.540 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWord,65] - Word文档导出完成，文件大小: 3038 bytes
22:37:26.565 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [testEmptyDataExport,156] - 空数据测试导出成功，文件名: %E7%A9%BA%E6%95%B0%E6%8D%AE%E6%B5%8B%E8%AF%95%E5%AF%BC%E5%87%BA_20250818_223726.docx, 大小: 3038 bytes
22:38:36.896 [http-nio-9550-exec-10] INFO  c.l.w.c.WordExportController - [exportTable,38] - 接收到表格导出请求，标题: 检验记录表
22:38:36.897 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [exportTableToWord,34] - 开始导出Word文档，表格标题: 检验记录表
22:38:36.901 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [setPageOrientation,544] - 已设置文档为横向纸张
22:38:36.902 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,86] - 接收到表头数据，共2行
22:38:36.903 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,89] - 第1行表头，共8列
22:38:36.904 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,93] -   列0: content='检查工序名称', rowspan=2, colspan=1
22:38:36.904 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,93] -   列1: content='检查项目及技术条件', rowspan=2, colspan=1
22:38:36.904 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,93] -   列2: content='实际检查结果', rowspan=2, colspan=1
22:38:36.905 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,93] -   列3: content='完工', rowspan=1, colspan=2
22:38:36.905 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,93] -   列5: content='操作员', rowspan=2, colspan=1
22:38:36.905 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,93] -   列6: content='班组长', rowspan=2, colspan=1
22:38:36.905 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,93] -   列7: content='检验员', rowspan=2, colspan=1
22:38:36.906 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,89] - 第2行表头，共8列
22:38:36.906 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,93] -   列3: content='月', rowspan=1, colspan=1
22:38:36.906 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,93] -   列4: content='日', rowspan=1, colspan=1
22:38:36.906 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,113] - 创建表格: 2行 x 8列
22:38:36.908 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,265] - 开始处理表头，共2行
22:38:36.909 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,271] - 处理第1行表头，共8列
22:38:36.918 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,271] - 处理第2行表头，共8列
22:38:36.919 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,280] - 表头处理完成，共处理2行
22:38:36.924 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,147] - 表格创建完成
22:38:36.933 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [exportTableToWord,65] - Word文档导出完成，文件大小: 2810 bytes
22:38:36.941 [http-nio-9550-exec-10] INFO  c.l.w.c.WordExportController - [exportTable,52] - 表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250818_223836.docx, 大小: 2810 bytes
22:38:46.342 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportTable,38] - 接收到表格导出请求，标题: 检验记录表
22:38:46.343 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWord,34] - 开始导出Word文档，表格标题: 检验记录表
22:38:46.345 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,544] - 已设置文档为横向纸张
22:38:46.347 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,86] - 接收到表头数据，共2行
22:38:46.347 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,89] - 第1行表头，共8列
22:38:46.347 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,93] -   列0: content='检查工序名称', rowspan=2, colspan=1
22:38:46.348 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,93] -   列1: content='检查项目及技术条件', rowspan=2, colspan=1
22:38:46.348 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,93] -   列2: content='实际检查结果', rowspan=2, colspan=1
22:38:46.348 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,93] -   列3: content='完工', rowspan=1, colspan=2
22:38:46.348 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,93] -   列5: content='操作员', rowspan=2, colspan=1
22:38:46.348 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,93] -   列6: content='班组长', rowspan=2, colspan=1
22:38:46.349 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,93] -   列7: content='检验员', rowspan=2, colspan=1
22:38:46.349 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,89] - 第2行表头，共8列
22:38:46.349 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,93] -   列3: content='月', rowspan=1, colspan=1
22:38:46.349 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,93] -   列4: content='日', rowspan=1, colspan=1
22:38:46.350 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,113] - 创建表格: 3行 x 8列
22:38:46.352 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,265] - 开始处理表头，共2行
22:38:46.353 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,271] - 处理第1行表头，共8列
22:38:46.364 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,271] - 处理第2行表头，共8列
22:38:46.365 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,280] - 表头处理完成，共处理2行
22:38:46.369 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,147] - 表格创建完成
22:38:46.381 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWord,65] - Word文档导出完成，文件大小: 2869 bytes
22:38:46.388 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportTable,52] - 表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250818_223846.docx, 大小: 2869 bytes
22:39:19.057 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
22:39:19.071 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
22:39:26.120 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 1095955 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
22:39:26.124 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
22:39:27.758 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
22:39:27.759 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
22:39:27.759 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
22:39:27.833 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
22:39:28.811 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
22:39:29.272 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
22:39:30.406 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
22:39:30.435 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 5.074 seconds (JVM running for 6.179)
22:39:42.726 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
22:39:42.944 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportTable,38] - 接收到表格导出请求，标题: 检验记录表
22:39:42.945 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWord,34] - 开始导出Word文档，表格标题: 检验记录表
22:39:43.602 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,417] - 已设置文档为横向纸张
22:39:43.699 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,80] - 接收到表头数据，共2行
22:39:43.700 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,83] - 第1行表头，共8列
22:39:43.700 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,87] -   列0: content='检查工序名称', rowspan=2, colspan=1
22:39:43.700 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,87] -   列1: content='检查项目及技术条件', rowspan=2, colspan=1
22:39:43.701 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,87] -   列2: content='实际检查结果', rowspan=2, colspan=1
22:39:43.701 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,87] -   列3: content='完工', rowspan=1, colspan=2
22:39:43.701 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,87] -   列5: content='操作员', rowspan=2, colspan=1
22:39:43.701 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,87] -   列6: content='班组长', rowspan=2, colspan=1
22:39:43.701 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,87] -   列7: content='检验员', rowspan=2, colspan=1
22:39:43.702 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,83] - 第2行表头，共8列
22:39:43.702 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,87] -   列3: content='月', rowspan=1, colspan=1
22:39:43.702 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,87] -   列4: content='日', rowspan=1, colspan=1
22:39:43.702 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,107] - 创建表格: 3行 x 8列
22:39:43.788 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,152] - 开始处理表头，共2行
22:39:43.789 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,158] - 处理第1行表头，共8列
22:39:43.865 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,158] - 处理第2行表头，共8列
22:39:43.874 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,167] - 表头处理完成，共处理2行
22:39:43.909 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,141] - 表格创建完成
22:39:44.101 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWord,59] - Word文档导出完成，文件大小: 2876 bytes
22:39:44.127 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportTable,52] - 表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250818_223944.docx, 大小: 2876 bytes
22:40:19.588 [http-nio-9550-exec-2] INFO  c.l.w.c.WordExportController - [exportTable,38] - 接收到表格导出请求，标题: 检验记录表
22:40:19.588 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [exportTableToWord,34] - 开始导出Word文档，表格标题: 检验记录表
22:40:19.591 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [setPageOrientation,417] - 已设置文档为横向纸张
22:40:19.594 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,80] - 接收到表头数据，共2行
22:40:19.594 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,83] - 第1行表头，共8列
22:40:19.595 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,87] -   列0: content='检查工序名称', rowspan=2, colspan=1
22:40:19.595 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,87] -   列1: content='检查项目及技术条件', rowspan=2, colspan=1
22:40:19.595 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,87] -   列2: content='实际检查结果', rowspan=2, colspan=1
22:40:19.595 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,87] -   列3: content='完工', rowspan=1, colspan=2
22:40:19.595 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,87] -   列5: content='操作员', rowspan=2, colspan=1
22:40:19.596 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,87] -   列6: content='班组长', rowspan=2, colspan=1
22:40:19.596 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,87] -   列7: content='检验员', rowspan=2, colspan=1
22:40:19.596 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,83] - 第2行表头，共8列
22:40:19.596 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,87] -   列3: content='月', rowspan=1, colspan=1
22:40:19.597 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,87] -   列4: content='日', rowspan=1, colspan=1
22:40:19.597 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,107] - 创建表格: 3行 x 8列
22:40:19.601 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,152] - 开始处理表头，共2行
22:40:19.601 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,158] - 处理第1行表头，共8列
22:40:19.616 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,158] - 处理第2行表头，共8列
22:40:19.620 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,167] - 表头处理完成，共处理2行
22:40:19.625 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,141] - 表格创建完成
22:40:19.639 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [exportTableToWord,59] - Word文档导出完成，文件大小: 2903 bytes
22:40:19.644 [http-nio-9550-exec-2] INFO  c.l.w.c.WordExportController - [exportTable,52] - 表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250818_224019.docx, 大小: 2903 bytes
22:42:01.454 [http-nio-9550-exec-9] INFO  c.l.w.c.WordExportController - [exportTable,38] - 接收到表格导出请求，标题: 检验记录表
22:42:01.455 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [exportTableToWord,34] - 开始导出Word文档，表格标题: 检验记录表
22:42:01.458 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [setPageOrientation,417] - 已设置文档为横向纸张
22:42:01.459 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [createTable,80] - 接收到表头数据，共2行
22:42:01.460 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [createTable,83] - 第1行表头，共8列
22:42:01.461 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [createTable,87] -   列0: content='检查工序名称', rowspan=2, colspan=1
22:42:01.462 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [createTable,87] -   列1: content='检查项目及技术条件', rowspan=2, colspan=1
22:42:01.462 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [createTable,87] -   列2: content='实际检查结果', rowspan=2, colspan=1
22:42:01.463 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [createTable,87] -   列3: content='完工', rowspan=1, colspan=2
22:42:01.463 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [createTable,87] -   列5: content='操作员', rowspan=2, colspan=1
22:42:01.463 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [createTable,87] -   列6: content='班组长', rowspan=2, colspan=1
22:42:01.463 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [createTable,87] -   列7: content='检验员', rowspan=2, colspan=1
22:42:01.464 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [createTable,83] - 第2行表头，共8列
22:42:01.464 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [createTable,87] -   列3: content='月', rowspan=1, colspan=1
22:42:01.464 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [createTable,87] -   列4: content='日', rowspan=1, colspan=1
22:42:01.464 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [createTable,107] - 创建表格: 3行 x 8列
22:42:01.467 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,152] - 开始处理表头，共2行
22:42:01.467 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,158] - 处理第1行表头，共8列
22:42:01.472 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,158] - 处理第2行表头，共8列
22:42:01.474 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,167] - 表头处理完成，共处理2行
22:42:01.480 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [createTable,141] - 表格创建完成
22:42:01.493 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [exportTableToWord,59] - Word文档导出完成，文件大小: 2921 bytes
22:42:01.500 [http-nio-9550-exec-9] INFO  c.l.w.c.WordExportController - [exportTable,52] - 表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250818_224201.docx, 大小: 2921 bytes
23:06:58.485 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
23:06:58.490 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
23:07:06.917 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 1130581 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
23:07:06.919 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
23:07:07.913 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
23:07:07.914 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
23:07:07.914 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
23:07:07.965 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
23:07:08.576 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
23:07:08.860 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
23:07:09.599 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
23:07:09.617 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 3.196 seconds (JVM running for 4.054)
23:07:30.792 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
23:08:38.828 [http-nio-9550-exec-7] INFO  c.l.w.c.WordExportController - [exportTable,38] - 接收到表格导出请求，标题: 检验记录表
23:08:38.829 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [exportTableToWord,35] - 开始导出Word文档，表格标题: 检验记录表
23:08:39.142 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [setPageOrientation,495] - 已设置文档为横向纸张
23:08:39.197 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,81] - 接收到表头数据，共2行
23:08:39.198 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,84] - 第1行表头，共8列
23:08:39.198 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,88] -   列0: content='检查工序名称', rowspan=2, colspan=1
23:08:39.198 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,88] -   列1: content='检查项目及技术条件', rowspan=2, colspan=1
23:08:39.198 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,88] -   列2: content='实际检查结果', rowspan=2, colspan=1
23:08:39.199 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,88] -   列3: content='完工', rowspan=1, colspan=2
23:08:39.199 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,88] -   列5: content='操作员', rowspan=2, colspan=1
23:08:39.199 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,88] -   列6: content='班组长', rowspan=2, colspan=1
23:08:39.199 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,88] -   列7: content='检验员', rowspan=2, colspan=1
23:08:39.199 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,84] - 第2行表头，共8列
23:08:39.199 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,88] -   列3: content='月', rowspan=1, colspan=1
23:08:39.200 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,88] -   列4: content='日', rowspan=1, colspan=1
23:08:39.200 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,108] - 创建表格: 3行 x 8列
23:08:39.247 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,153] - 开始处理表头，共2行
23:08:39.247 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,159] - 处理第1行表头，共8列
23:08:39.285 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,159] - 处理第2行表头，共8列
23:08:39.288 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,168] - 表头处理完成，共处理2行
23:08:39.311 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,142] - 表格创建完成
23:08:39.415 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [exportTableToWord,60] - Word文档导出完成，文件大小: 2946 bytes
23:08:39.424 [http-nio-9550-exec-7] INFO  c.l.w.c.WordExportController - [exportTable,52] - 表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250818_230839.docx, 大小: 2946 bytes
23:13:13.865 [http-nio-9550-exec-2] INFO  c.l.w.c.WordExportController - [exportTable,38] - 接收到表格导出请求，标题: 检验记录表
23:13:13.866 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [exportTableToWord,35] - 开始导出Word文档，表格标题: 检验记录表
23:13:13.875 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [setPageOrientation,495] - 已设置文档为横向纸张
23:13:13.880 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,81] - 接收到表头数据，共2行
23:13:13.881 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,84] - 第1行表头，共8列
23:13:13.882 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,88] -   列0: content='检查工序名称', rowspan=2, colspan=1
23:13:13.882 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,88] -   列1: content='检查项目及技术条件', rowspan=2, colspan=1
23:13:13.882 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,88] -   列2: content='实际检查结果', rowspan=2, colspan=1
23:13:13.883 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,88] -   列3: content='完工', rowspan=1, colspan=2
23:13:13.883 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,88] -   列5: content='操作员', rowspan=2, colspan=1
23:13:13.883 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,88] -   列6: content='班组长', rowspan=2, colspan=1
23:13:13.883 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,88] -   列7: content='检验员', rowspan=2, colspan=1
23:13:13.884 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,84] - 第2行表头，共8列
23:13:13.884 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,88] -   列3: content='月', rowspan=1, colspan=1
23:13:13.884 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,88] -   列4: content='日', rowspan=1, colspan=1
23:13:13.885 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,108] - 创建表格: 3行 x 8列
23:13:13.890 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,153] - 开始处理表头，共2行
23:13:13.891 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,159] - 处理第1行表头，共8列
23:13:13.910 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,159] - 处理第2行表头，共8列
23:13:13.913 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,168] - 表头处理完成，共处理2行
23:13:14.047 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,142] - 表格创建完成
23:13:14.081 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [exportTableToWord,60] - Word文档导出完成，文件大小: 2884 bytes
23:13:14.098 [http-nio-9550-exec-2] INFO  c.l.w.c.WordExportController - [exportTable,52] - 表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250818_231314.docx, 大小: 2884 bytes
23:20:58.900 [http-nio-9550-exec-4] INFO  c.l.w.c.WordExportController - [exportTable,38] - 接收到表格导出请求，标题: 检验记录表
23:20:58.900 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [exportTableToWord,35] - 开始导出Word文档，表格标题: 检验记录表
23:20:58.902 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [setPageOrientation,495] - 已设置文档为横向纸张
23:20:58.902 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [createTable,81] - 接收到表头数据，共2行
23:20:58.903 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [createTable,84] - 第1行表头，共8列
23:20:58.903 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [createTable,88] -   列0: content='检查工序名称', rowspan=2, colspan=1
23:20:58.903 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [createTable,88] -   列1: content='检查项目及技术条件', rowspan=2, colspan=1
23:20:58.903 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [createTable,88] -   列2: content='实际检查结果', rowspan=2, colspan=1
23:20:58.903 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [createTable,88] -   列3: content='完工', rowspan=1, colspan=2
23:20:58.903 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [createTable,88] -   列5: content='操作员', rowspan=2, colspan=1
23:20:58.903 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [createTable,88] -   列6: content='班组长', rowspan=2, colspan=1
23:20:58.903 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [createTable,88] -   列7: content='检验员', rowspan=2, colspan=1
23:20:58.904 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [createTable,84] - 第2行表头，共8列
23:20:58.904 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [createTable,88] -   列3: content='月', rowspan=1, colspan=1
23:20:58.904 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [createTable,88] -   列4: content='日', rowspan=1, colspan=1
23:20:58.904 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [createTable,108] - 创建表格: 3行 x 8列
23:20:58.905 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,153] - 开始处理表头，共2行
23:20:58.905 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,159] - 处理第1行表头，共8列
23:20:58.908 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,159] - 处理第2行表头，共8列
23:20:58.909 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,168] - 表头处理完成，共处理2行
23:20:58.916 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [createTable,142] - 表格创建完成
23:20:58.923 [http-nio-9550-exec-4] INFO  c.l.w.s.WordExportService - [exportTableToWord,60] - Word文档导出完成，文件大小: 2883 bytes
23:20:58.928 [http-nio-9550-exec-4] INFO  c.l.w.c.WordExportController - [exportTable,52] - 表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250818_232058.docx, 大小: 2883 bytes
23:27:26.864 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 1156375 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
23:27:26.866 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
23:27:28.066 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
23:27:28.067 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
23:27:28.068 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
23:27:28.129 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
23:27:28.860 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
23:27:29.293 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
23:27:30.172 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
23:27:30.293 [main] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
23:27:30.295 [main] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
23:27:30.298 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Pausing ProtocolHandler ["http-nio-9550"]
23:27:30.299 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
23:27:30.302 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-9550"]
23:27:30.303 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Destroying ProtocolHandler ["http-nio-9550"]
23:28:05.941 [http-nio-9550-exec-10] INFO  c.l.w.c.WordExportController - [exportTable,38] - 接收到表格导出请求，标题: 检验记录表
23:28:05.941 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [exportTableToWord,35] - 开始导出Word文档，表格标题: 检验记录表
23:28:05.943 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [setPageOrientation,495] - 已设置文档为横向纸张
23:28:05.944 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,81] - 接收到表头数据，共2行
23:28:05.945 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,84] - 第1行表头，共8列
23:28:05.945 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,88] -   列0: content='检查工序名称', rowspan=2, colspan=1
23:28:05.945 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,88] -   列1: content='检查项目及技术条件', rowspan=2, colspan=1
23:28:05.946 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,88] -   列2: content='实际检查结果', rowspan=2, colspan=1
23:28:05.946 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,88] -   列3: content='完工', rowspan=1, colspan=2
23:28:05.946 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,88] -   列5: content='操作员', rowspan=2, colspan=1
23:28:05.946 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,88] -   列6: content='班组长', rowspan=2, colspan=1
23:28:05.946 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,88] -   列7: content='检验员', rowspan=2, colspan=1
23:28:05.946 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,84] - 第2行表头，共8列
23:28:05.947 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,88] -   列3: content='月', rowspan=1, colspan=1
23:28:05.947 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,88] -   列4: content='日', rowspan=1, colspan=1
23:28:05.948 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,108] - 创建表格: 3行 x 8列
23:28:05.949 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,153] - 开始处理表头，共2行
23:28:05.950 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,159] - 处理第1行表头，共8列
23:28:05.955 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,159] - 处理第2行表头，共8列
23:28:05.957 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,168] - 表头处理完成，共处理2行
23:28:05.962 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,142] - 表格创建完成
23:28:05.967 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [exportTableToWord,60] - Word文档导出完成，文件大小: 2887 bytes
23:28:05.970 [http-nio-9550-exec-10] INFO  c.l.w.c.WordExportController - [exportTable,52] - 表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250818_232805.docx, 大小: 2887 bytes
23:30:48.354 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 1161120 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
23:30:48.355 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
23:30:49.267 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
23:30:49.267 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
23:30:49.268 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
23:30:49.317 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
23:30:49.855 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
23:30:50.123 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
23:30:50.745 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
23:30:50.761 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 2.801 seconds (JVM running for 3.494)
23:30:56.944 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
23:30:57.051 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportTable,38] - 接收到表格导出请求，标题: 检验记录表
23:30:57.051 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWord,35] - 开始导出Word文档，表格标题: 检验记录表
23:30:57.388 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,495] - 已设置文档为横向纸张
23:30:57.426 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,81] - 接收到表头数据，共2行
23:30:57.426 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,84] - 第1行表头，共8列
23:30:57.426 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,88] -   列0: content='检查工序名称', rowspan=2, colspan=1
23:30:57.427 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,88] -   列1: content='检查项目及技术条件', rowspan=2, colspan=1
23:30:57.427 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,88] -   列2: content='实际检查结果', rowspan=2, colspan=1
23:30:57.427 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,88] -   列3: content='完工', rowspan=1, colspan=2
23:30:57.427 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,88] -   列5: content='操作员', rowspan=2, colspan=1
23:30:57.427 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,88] -   列6: content='班组长', rowspan=2, colspan=1
23:30:57.427 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,88] -   列7: content='检验员', rowspan=2, colspan=1
23:30:57.427 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,84] - 第2行表头，共8列
23:30:57.427 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,88] -   列3: content='月', rowspan=1, colspan=1
23:30:57.427 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,88] -   列4: content='日', rowspan=1, colspan=1
23:30:57.427 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,108] - 创建表格: 3行 x 8列
23:30:57.459 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,153] - 开始处理表头，共2行
23:30:57.460 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,159] - 处理第1行表头，共8列
23:30:57.485 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,159] - 处理第2行表头，共8列
23:30:57.487 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,168] - 表头处理完成，共处理2行
23:30:57.526 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,142] - 表格创建完成
23:30:57.588 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWord,60] - Word文档导出完成，文件大小: 2887 bytes
23:30:57.599 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportTable,52] - 表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250818_233057.docx, 大小: 2887 bytes
23:31:50.432 [http-nio-9550-exec-2] INFO  c.l.w.c.WordExportController - [exportTable,38] - 接收到表格导出请求，标题: 检验记录表
23:31:50.433 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [exportTableToWord,35] - 开始导出Word文档，表格标题: 检验记录表
23:31:50.437 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [setPageOrientation,495] - 已设置文档为横向纸张
23:31:50.438 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,81] - 接收到表头数据，共2行
23:31:50.438 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,84] - 第1行表头，共8列
23:31:50.438 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,88] -   列0: content='检查工序名称', rowspan=2, colspan=1
23:31:50.438 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,88] -   列1: content='检查项目及技术条件', rowspan=2, colspan=1
23:31:50.438 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,88] -   列2: content='实际检查结果', rowspan=2, colspan=1
23:31:50.438 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,88] -   列3: content='完工', rowspan=1, colspan=2
23:31:50.438 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,88] -   列5: content='操作员', rowspan=2, colspan=1
23:31:50.438 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,88] -   列6: content='班组长', rowspan=2, colspan=1
23:31:50.439 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,88] -   列7: content='检验员', rowspan=2, colspan=1
23:31:50.439 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,84] - 第2行表头，共8列
23:31:50.439 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,88] -   列3: content='月', rowspan=1, colspan=1
23:31:50.439 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,88] -   列4: content='日', rowspan=1, colspan=1
23:31:50.439 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,108] - 创建表格: 3行 x 8列
23:31:50.441 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,153] - 开始处理表头，共2行
23:31:50.442 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,159] - 处理第1行表头，共8列
23:31:50.450 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,159] - 处理第2行表头，共8列
23:31:50.451 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,168] - 表头处理完成，共处理2行
23:32:39.994 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,142] - 表格创建完成
23:32:40.003 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [exportTableToWord,60] - Word文档导出完成，文件大小: 2887 bytes
23:32:40.008 [http-nio-9550-exec-2] INFO  c.l.w.c.WordExportController - [exportTable,52] - 表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250818_233240.docx, 大小: 2887 bytes
