package com.logictrue.word.dto;

import lombok.Data;
import java.util.List;

/**
 * 表格导出请求DTO
 */
@Data
public class TableExportRequest {
    
    /**
     * 表格标题
     */
    private String title;
    
    /**
     * 表格宽度（像素）
     */
    private Integer tableWidth;
    
    /**
     * 表格高度（像素）
     */
    private Integer tableHeight;

    /**
     * 纸张方向 (PORTRAIT: 纵向, LANDSCAPE: 横向)
     */
    private String pageOrientation;

    /**
     * 表格数据
     */
    private TableData tableData;
    
    @Data
    public static class TableData {
        /**
         * 表头数据
         */
        private List<List<HeaderCell>> headers;
        
        /**
         * 数据行
         */
        private List<List<DataCell>> dataRows;
    }
    
    @Data
    public static class HeaderCell {
        /**
         * 单元格内容
         */
        private String content;
        
        /**
         * 行跨度
         */
        private Integer rowspan;
        
        /**
         * 列跨度
         */
        private Integer colspan;
        
        /**
         * 宽度（像素）
         */
        private Integer width;
        
        /**
         * 高度（像素）
         */
        private Integer height;
    }
    
    @Data
    public static class DataCell {
        /**
         * 单元格内容
         */
        private String content;
        
        /**
         * 是否包含数学公式
         */
        private Boolean hasMath;
        
        /**
         * 宽度（像素）
         */
        private Integer width;
        
        /**
         * 高度（像素）
         */
        private Integer height;
    }
}
