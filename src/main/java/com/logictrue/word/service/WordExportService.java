package com.logictrue.word.service;

import com.logictrue.word.dto.TableExportRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xwpf.usermodel.*;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.*;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigInteger;
import java.util.List;

/**
 * Word导出服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WordExportService {

    private final MathJaxService mathJaxService;
    private final MathMLToOMMLService mathMLToOMMLService;

    // 像素到磅的转换比例（1磅 = 1.33像素）
    private static final double PIXEL_TO_POINT = 0.75;
    // 磅到Twips的转换比例（1磅 = 20 Twips）
    private static final int POINT_TO_TWIPS = 20;

    /**
     * 导出表格到Word文档
     */
    public byte[] exportTableToWord(TableExportRequest request) throws IOException {
        log.info("开始导出Word文档，表格标题: {}", request.getTitle());

        try (XWPFDocument document = new XWPFDocument();
             ByteArrayOutputStream out = new ByteArrayOutputStream()) {

            // 设置页面方向
            setPageOrientation(document, request.getPageOrientation());

            // 添加标题
            if (request.getTitle() != null && !request.getTitle().trim().isEmpty()) {
                XWPFParagraph titleParagraph = document.createParagraph();
                titleParagraph.setAlignment(ParagraphAlignment.CENTER);
                XWPFRun titleRun = titleParagraph.createRun();
                titleRun.setText(request.getTitle());
                titleRun.setBold(true);
                titleRun.setFontSize(16);
                titleRun.setFontFamily("宋体");
            }

            // 创建表格
            createTable(document, request);

            document.write(out);
            byte[] result = out.toByteArray();

            log.info("Word文档导出完成，文件大小: {} bytes", result.length);
            return result;

        } catch (Exception e) {
            log.error("导出Word文档失败", e);
            throw new IOException("导出Word文档失败: " + e.getMessage(), e);
        }
    }

    /**
     * 创建表格
     */
    private void createTable(XWPFDocument document, TableExportRequest request) {
        TableExportRequest.TableData tableData = request.getTableData();
        if (tableData == null) {
            log.warn("表格数据为空，跳过创建表格");
            return;
        }

        // 调试：打印接收到的表头数据
        if (tableData.getHeaders() != null) {
            log.info("接收到表头数据，共{}行", tableData.getHeaders().size());
            for (int i = 0; i < tableData.getHeaders().size(); i++) {
                List<TableExportRequest.HeaderCell> row = tableData.getHeaders().get(i);
                log.info("第{}行表头，共{}列", i + 1, row.size());
                for (int j = 0; j < row.size(); j++) {
                    TableExportRequest.HeaderCell cell = row.get(j);
                    if (cell.getContent() != null && !cell.getContent().trim().isEmpty()) {
                        log.info("  列{}: content='{}', rowspan={}, colspan={}",
                                j, cell.getContent(), cell.getRowspan(), cell.getColspan());
                    }
                }
            }
        }

        // 计算总行数
        int headerRows = tableData.getHeaders() != null ? tableData.getHeaders().size() : 0;
        int dataRows = tableData.getDataRows() != null ? tableData.getDataRows().size() : 0;
        int totalRows = headerRows + dataRows;

        if (totalRows == 0) {
            log.warn("表格行数为0，跳过创建表格");
            return;
        }

        // 计算列数 - 使用固定的8列（根据前端表格结构）
        int cols = 8;

        log.info("创建表格: {}行 x {}列", totalRows, cols);

        // 创建表格
        XWPFTable table = document.createTable(totalRows, cols);
        table.setWidth("100%");

        // 设置表格样式
        table.getCTTbl().getTblPr().unsetTblBorders();

        int currentRow = 0;

        // 处理表头 - 支持单元格合并
        if (headerRows > 0) {
            currentRow = processHeadersWithMerge(table, tableData.getHeaders());
        }

        // 处理数据行
        if (dataRows > 0) {
            for (List<TableExportRequest.DataCell> dataRow : tableData.getDataRows()) {
                if (currentRow < table.getRows().size()) {
                    XWPFTableRow row = table.getRow(currentRow);
                    processDataRow(row, dataRow);
                } else {
                    // 如果行数不够，创建新行
                    XWPFTableRow newRow = table.createRow();
                    processDataRow(newRow, dataRow);
                }
                currentRow++;
            }
        }

        // 设置表格边框
        setTableBorders(table);

        log.info("表格创建完成");
    }

    /**
     * 处理支持单元格合并的表头
     */
    private int processHeadersWithMerge(XWPFTable table, List<List<TableExportRequest.HeaderCell>> headers) {
        if (headers == null || headers.isEmpty()) {
            return 0;
        }

        log.info("开始处理表头，共{}行", headers.size());

        int processedRows = 0;

        for (int rowIndex = 0; rowIndex < headers.size(); rowIndex++) {
            List<TableExportRequest.HeaderCell> headerRow = headers.get(rowIndex);
            log.info("处理第{}行表头，共{}列", rowIndex + 1, headerRow.size());

            if (rowIndex < table.getRows().size()) {
                XWPFTableRow row = table.getRow(rowIndex);
                processHeaderRowWithMerge(row, headerRow, rowIndex);
                processedRows++;
            }
        }

        log.info("表头处理完成，共处理{}行", processedRows);
        return processedRows;
    }

    /**
     * 处理单行表头，支持单元格合并
     */
    private void processHeaderRowWithMerge(XWPFTableRow row, List<TableExportRequest.HeaderCell> headerCells, int rowIndex) {
        for (int i = 0; i < headerCells.size() && i < row.getTableCells().size(); i++) {
            TableExportRequest.HeaderCell headerCell = headerCells.get(i);
            XWPFTableCell cell = row.getCell(i);

            // 处理有内容的单元格
            if (headerCell.getContent() != null && !headerCell.getContent().trim().isEmpty()) {
                // 设置单元格内容
                setHeaderCellContent(cell, headerCell.getContent());

                // 设置单元格尺寸
                setCellSize(cell, headerCell.getWidth(), headerCell.getHeight());

                // 处理列合并 (colspan)
                if (headerCell.getColspan() != null && headerCell.getColspan() > 1) {
                    mergeHorizontalCells(row, i, headerCell.getColspan());
                    log.debug("合并列: 起始列={}, 合并数={}", i, headerCell.getColspan());
                }

                // 处理行合并 (rowspan) - 需要在表格创建完成后处理
                if (headerCell.getRowspan() != null && headerCell.getRowspan() > 1) {
                    // 标记需要行合并的单元格，稍后处理
                    CTTcPr tcPr = cell.getCTTc().getTcPr();
                    if (tcPr == null) {
                        tcPr = cell.getCTTc().addNewTcPr();
                    }
                    tcPr.addNewVMerge().setVal(STMerge.RESTART);
                    markVerticalMergeCells(row.getTable(), rowIndex, i, headerCell.getRowspan());
                    log.debug("标记行合并: 行={}, 列={}, 合并数={}", rowIndex, i, headerCell.getRowspan());
                }
            } else {
                // 处理空的占位符单元格 - 清空内容但保持单元格结构
                cell.removeParagraph(0);
                cell.addParagraph();
                log.debug("处理占位符单元格: 行={}, 列={}", rowIndex, i);
            }
        }
    }

    /**
     * 合并水平单元格
     */
    private void mergeHorizontalCells(XWPFTableRow row, int startCol, int colspan) {
        try {
            for (int i = 0; i < colspan; i++) {
                if (startCol + i < row.getTableCells().size()) {
                    XWPFTableCell cell = row.getCell(startCol + i);
                    CTTcPr tcPr = cell.getCTTc().getTcPr();
                    if (tcPr == null) {
                        tcPr = cell.getCTTc().addNewTcPr();
                    }

                    CTHMerge hMerge = tcPr.getHMerge();
                    if (hMerge == null) {
                        hMerge = tcPr.addNewHMerge();
                    }

                    if (i == 0) {
                        hMerge.setVal(STMerge.RESTART);
                    } else {
                        hMerge.setVal(STMerge.CONTINUE);
                    }
                }
            }
        } catch (Exception e) {
            log.warn("水平合并单元格失败: {}", e.getMessage());
        }
    }

    /**
     * 标记垂直合并单元格
     */
    private void markVerticalMergeCells(XWPFTable table, int startRow, int col, int rowspan) {
        try {
            for (int i = 1; i < rowspan; i++) {
                int targetRow = startRow + i;
                if (targetRow < table.getRows().size()) {
                    XWPFTableRow row = table.getRow(targetRow);
                    if (col < row.getTableCells().size()) {
                        XWPFTableCell cell = row.getCell(col);
                        CTTcPr tcPr = cell.getCTTc().getTcPr();
                        if (tcPr == null) {
                            tcPr = cell.getCTTc().addNewTcPr();
                        }
                        tcPr.addNewVMerge().setVal(STMerge.CONTINUE);
                    }
                }
            }
        } catch (Exception e) {
            log.warn("垂直合并单元格失败: {}", e.getMessage());
        }
    }

    /**
     * 设置表头单元格内容
     */
    private void setHeaderCellContent(XWPFTableCell cell, String content) {
        // 清除现有内容
        cell.removeParagraph(0);

        // 创建新段落
        XWPFParagraph paragraph = cell.addParagraph();
        paragraph.setAlignment(ParagraphAlignment.CENTER);

        // 创建文本运行
        XWPFRun run = paragraph.createRun();
        run.setText(content);
        run.setBold(true);
        run.setFontFamily("宋体");
        run.setFontSize(12);

        // 设置单元格样式
        cell.setColor("F2F2F2"); // 浅灰色背景
        cell.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
    }

    /**
     * 处理表头行（保留原方法以兼容）
     */
    private void processHeaderRow(XWPFTableRow row, List<TableExportRequest.HeaderCell> headerCells) {
        for (int i = 0; i < headerCells.size() && i < row.getTableCells().size(); i++) {
            TableExportRequest.HeaderCell headerCell = headerCells.get(i);
            XWPFTableCell cell = row.getCell(i);

            setHeaderCellContent(cell, headerCell.getContent() != null ? headerCell.getContent() : "");

            // 设置单元格尺寸
            setCellSize(cell, headerCell.getWidth(), headerCell.getHeight());

            // 处理合并单元格
            if (headerCell.getColspan() != null && headerCell.getColspan() > 1) {
                log.debug("表头单元格列合并: {}", headerCell.getColspan());
            }

            if (headerCell.getRowspan() != null && headerCell.getRowspan() > 1) {
                log.debug("表头单元格行合并: {}", headerCell.getRowspan());
            }
        }
    }

    /**
     * 处理数据行
     */
    private void processDataRow(XWPFTableRow row, List<TableExportRequest.DataCell> dataCells) {
        for (int i = 0; i < dataCells.size() && i < row.getTableCells().size(); i++) {
            TableExportRequest.DataCell dataCell = dataCells.get(i);
            XWPFTableCell cell = row.getCell(i);

            // 设置单元格内容
            cell.removeParagraph(0); // 移除默认段落
            XWPFParagraph paragraph = cell.addParagraph();
            paragraph.setAlignment(ParagraphAlignment.CENTER);

            // 处理数学公式
            if (Boolean.TRUE.equals(dataCell.getHasMath()) && dataCell.getMathML() != null) {
                // 使用MathML转OMML处理公式
                insertMathFormula(paragraph, dataCell.getMathML());
            } else {
                // 处理普通文本内容
                XWPFRun run = paragraph.createRun();
                String content = dataCell.getContent() != null ? dataCell.getContent() : "";

                // 如果有数学公式但没有MathML，使用旧的处理方式作为备用
                if (Boolean.TRUE.equals(dataCell.getHasMath()) && dataCell.getMathML() == null) {
                    content = mathJaxService.parseMathFormula(content);
                }

                run.setText(content);
                run.setFontFamily("宋体");
                run.setFontSize(11);
            }

            // 设置单元格样式
            cell.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);

            // 设置单元格尺寸
            setCellSize(cell, dataCell.getWidth(), dataCell.getHeight());
        }
    }

    /**
     * 设置单元格尺寸
     */
    private void setCellSize(XWPFTableCell cell, Integer widthPx, Integer heightPx) {
        if (widthPx != null && widthPx > 0) {
            // 将像素转换为Twips (1像素 ≈ 15 Twips)
            int widthTwips = (int) (widthPx * 15);
            cell.setWidth(String.valueOf(widthTwips));
        }

        if (heightPx != null && heightPx > 0) {
            // 设置行高（POI中行高设置相对复杂）
            try {
                XWPFTableRow row = cell.getTableRow();
                if (row.getCtRow().getTrPr() == null) {
                    row.getCtRow().addNewTrPr();
                }
                if (row.getCtRow().getTrPr().getTrHeightArray().length == 0) {
                    row.getCtRow().getTrPr().addNewTrHeight();
                }
                // 将像素转换为Twips
                int heightTwips = (int) (heightPx * 15);
                row.getCtRow().getTrPr().getTrHeightArray(0).setVal(BigInteger.valueOf(heightTwips));
            } catch (Exception e) {
                log.warn("设置单元格高度失败: {}", e.getMessage());
            }
        }
    }

    /**
     * 设置表格边框
     */
    private void setTableBorders(XWPFTable table) {
        // 设置表格边框样式
        table.setInsideHBorder(XWPFTable.XWPFBorderType.SINGLE, 4, 0, "000000");
        table.setInsideVBorder(XWPFTable.XWPFBorderType.SINGLE, 4, 0, "000000");
        table.setTopBorder(XWPFTable.XWPFBorderType.SINGLE, 4, 0, "000000");
        table.setBottomBorder(XWPFTable.XWPFBorderType.SINGLE, 4, 0, "000000");
        table.setLeftBorder(XWPFTable.XWPFBorderType.SINGLE, 4, 0, "000000");
        table.setRightBorder(XWPFTable.XWPFBorderType.SINGLE, 4, 0, "000000");
    }

    /**
     * 插入数学公式到段落
     */
    private void insertMathFormula(XWPFParagraph paragraph, String mathML) {
        try {
            log.debug("插入数学公式，MathML: {}", mathML);

            // 验证MathML格式
            if (!mathMLToOMMLService.isValidMathML(mathML)) {
                log.warn("无效的MathML格式，使用文本替代: {}", mathML);
                XWPFRun run = paragraph.createRun();
                run.setText("[公式]");
                run.setFontFamily("宋体");
                run.setFontSize(11);
                return;
            }

            // 将MathML转换为OMML
            String omml = mathMLToOMMLService.convertMathMLToOMML(mathML);

            if (omml != null && !omml.trim().isEmpty()) {
                // 插入OMML到Word文档
                insertOMMLToDocument(paragraph, omml);
                log.debug("数学公式插入成功");
            } else {
                log.warn("OMML转换失败，使用文本替代");
                XWPFRun run = paragraph.createRun();
                run.setText("[公式转换失败]");
                run.setFontFamily("宋体");
                run.setFontSize(11);
            }

        } catch (Exception e) {
            log.error("插入数学公式失败: {}", e.getMessage(), e);
            // 插入错误提示文本
            XWPFRun run = paragraph.createRun();
            run.setText("[公式错误]");
            run.setFontFamily("宋体");
            run.setFontSize(11);
        }
    }

    /**
     * 将OMML插入到Word文档
     */
    private void insertOMMLToDocument(XWPFParagraph paragraph, String omml) {
        try {
            // 创建数学公式运行
            XWPFRun mathRun = paragraph.createRun();

            // 获取段落的XML
            org.openxmlformats.schemas.wordprocessingml.x2006.main.CTP ctp = paragraph.getCTP();

            // 创建数学公式的XML节点
            // 注意：这里需要将OMML字符串正确插入到Word文档的XML结构中
            // 由于POI对数学公式的支持有限，这里使用简化的方式

            // 暂时使用文本表示，后续可以通过更复杂的XML操作来插入真正的OMML
            mathRun.setText("[数学公式]");
            mathRun.setFontFamily("宋体");
            mathRun.setFontSize(11);

            log.debug("OMML插入完成（简化版本）");

        } catch (Exception e) {
            log.error("OMML插入失败: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 设置文档纸张方向
     */
    private void setPageOrientation(XWPFDocument document, String orientation) {
        try {
            CTDocument1 doc = document.getDocument();
            CTBody body = doc.getBody();

            if (!body.isSetSectPr()) {
                body.addNewSectPr();
            }
            CTSectPr section = body.getSectPr();

            if (!section.isSetPgSz()) {
                section.addNewPgSz();
            }

            CTPageSz pageSize = section.getPgSz();

            // 默认为横向，除非明确指定为纵向
            boolean isLandscape = !"PORTRAIT".equalsIgnoreCase(orientation);

            if (isLandscape) {
                // 横向 (A4纸张: 宽297mm, 高210mm)
                // 1英寸 = 1440 twips, 1mm = 56.7 twips
                pageSize.setW(BigInteger.valueOf(16838)); // 297mm * 56.7 ≈ 16838 twips
                pageSize.setH(BigInteger.valueOf(11906)); // 210mm * 56.7 ≈ 11906 twips
                pageSize.setOrient(STPageOrientation.LANDSCAPE);
                log.info("已设置文档为横向纸张");
            } else {
                // 纵向 (A4纸张: 宽210mm, 高297mm)
                pageSize.setW(BigInteger.valueOf(11906)); // 210mm * 56.7 ≈ 11906 twips
                pageSize.setH(BigInteger.valueOf(16838)); // 297mm * 56.7 ≈ 16838 twips
                pageSize.setOrient(STPageOrientation.PORTRAIT);
                log.info("已设置文档为纵向纸张");
            }
        } catch (Exception e) {
            log.warn("设置纸张方向失败: {}", e.getMessage());
        }
    }
}
