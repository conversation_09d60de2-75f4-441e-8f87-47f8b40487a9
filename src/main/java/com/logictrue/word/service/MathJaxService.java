package com.logictrue.word.service;

import org.springframework.stereotype.Service;
import java.util.regex.Pattern;
import java.util.regex.Matcher;

/**
 * MathJax公式解析服务
 */
@Service
public class MathJaxService {
    
    // 数学公式匹配模式
    private static final Pattern INLINE_MATH_PATTERN = Pattern.compile("\\$([^$]+)\\$");
    private static final Pattern DISPLAY_MATH_PATTERN = Pattern.compile("\\$\\$([^$]+)\\$\\$");
    private static final Pattern LATEX_INLINE_PATTERN = Pattern.compile("\\\\\\(([^)]+)\\\\\\)");
    private static final Pattern LATEX_DISPLAY_PATTERN = Pattern.compile("\\\\\\[([^\\]]+)\\\\\\]");
    
    /**
     * 检测内容是否包含数学公式
     */
    public boolean containsMath(String content) {
        if (content == null || content.trim().isEmpty()) {
            return false;
        }
        
        return INLINE_MATH_PATTERN.matcher(content).find() ||
               DISPLAY_MATH_PATTERN.matcher(content).find() ||
               LATEX_INLINE_PATTERN.matcher(content).find() ||
               LATEX_DISPLAY_PATTERN.matcher(content).find();
    }
    
    /**
     * 解析数学公式，转换为Word可识别的格式
     * 这里简化处理，将LaTeX公式转换为普通文本表示
     */
    public String parseMathFormula(String content) {
        if (content == null || !containsMath(content)) {
            return content;
        }

        String result = content;

        // 处理显示公式 $$...$$
        result = processDisplayMath(result);

        // 处理行内公式 $...$
        result = processInlineMath(result);

        // 处理LaTeX显示公式 \[...\]
        result = processLatexDisplayMath(result);

        // 处理LaTeX行内公式 \(...\)
        result = processLatexInlineMath(result);

        return result;
    }

    /**
     * 处理显示公式 $$...$$
     */
    private String processDisplayMath(String content) {
        Matcher matcher = DISPLAY_MATH_PATTERN.matcher(content);
        StringBuffer sb = new StringBuffer();
        while (matcher.find()) {
            String formula = matcher.group(1);
            String replacement = "[公式: " + convertLatexToText(formula) + "]";
            matcher.appendReplacement(sb, Matcher.quoteReplacement(replacement));
        }
        matcher.appendTail(sb);
        return sb.toString();
    }

    /**
     * 处理行内公式 $...$
     */
    private String processInlineMath(String content) {
        Matcher matcher = INLINE_MATH_PATTERN.matcher(content);
        StringBuffer sb = new StringBuffer();
        while (matcher.find()) {
            String formula = matcher.group(1);
            String replacement = convertLatexToText(formula);
            matcher.appendReplacement(sb, Matcher.quoteReplacement(replacement));
        }
        matcher.appendTail(sb);
        return sb.toString();
    }

    /**
     * 处理LaTeX显示公式 \[...\]
     */
    private String processLatexDisplayMath(String content) {
        Matcher matcher = LATEX_DISPLAY_PATTERN.matcher(content);
        StringBuffer sb = new StringBuffer();
        while (matcher.find()) {
            String formula = matcher.group(1);
            String replacement = "[公式: " + convertLatexToText(formula) + "]";
            matcher.appendReplacement(sb, Matcher.quoteReplacement(replacement));
        }
        matcher.appendTail(sb);
        return sb.toString();
    }

    /**
     * 处理LaTeX行内公式 \(...\)
     */
    private String processLatexInlineMath(String content) {
        Matcher matcher = LATEX_INLINE_PATTERN.matcher(content);
        StringBuffer sb = new StringBuffer();
        while (matcher.find()) {
            String formula = matcher.group(1);
            String replacement = convertLatexToText(formula);
            matcher.appendReplacement(sb, Matcher.quoteReplacement(replacement));
        }
        matcher.appendTail(sb);
        return sb.toString();
    }
    
    /**
     * 将LaTeX公式转换为可读文本
     * 这是一个简化的转换，实际项目中可能需要更复杂的处理
     */
    private String convertLatexToText(String latex) {
        String result = latex;
        
        // 基本符号替换
        result = result.replaceAll("\\\\alpha", "α");
        result = result.replaceAll("\\\\beta", "β");
        result = result.replaceAll("\\\\gamma", "γ");
        result = result.replaceAll("\\\\delta", "δ");
        result = result.replaceAll("\\\\epsilon", "ε");
        result = result.replaceAll("\\\\pi", "π");
        result = result.replaceAll("\\\\sigma", "σ");
        result = result.replaceAll("\\\\theta", "θ");
        result = result.replaceAll("\\\\lambda", "λ");
        result = result.replaceAll("\\\\mu", "μ");
        result = result.replaceAll("\\\\omega", "ω");
        
        // 数学运算符
        result = result.replaceAll("\\\\sum", "∑");
        result = result.replaceAll("\\\\prod", "∏");
        result = result.replaceAll("\\\\int", "∫");
        result = result.replaceAll("\\\\infty", "∞");
        result = result.replaceAll("\\\\pm", "±");
        result = result.replaceAll("\\\\mp", "∓");
        result = result.replaceAll("\\\\times", "×");
        result = result.replaceAll("\\\\div", "÷");
        result = result.replaceAll("\\\\leq", "≤");
        result = result.replaceAll("\\\\geq", "≥");
        result = result.replaceAll("\\\\neq", "≠");
        result = result.replaceAll("\\\\approx", "≈");
        
        // 处理上下标
        result = result.replaceAll("\\^\\{([^}]+)\\}", "^($1)");
        result = result.replaceAll("_\\{([^}]+)\\}", "_($1)");
        result = result.replaceAll("\\^([a-zA-Z0-9])", "^$1");
        result = result.replaceAll("_([a-zA-Z0-9])", "_$1");
        
        // 处理分数
        result = result.replaceAll("\\\\frac\\{([^}]+)\\}\\{([^}]+)\\}", "($1)/($2)");
        
        // 处理根号
        result = result.replaceAll("\\\\sqrt\\{([^}]+)\\}", "√($1)");
        result = result.replaceAll("\\\\sqrt\\[([^\\]]+)\\]\\{([^}]+)\\}", "$1√($2)");
        
        // 处理极限
        result = result.replaceAll("\\\\lim", "lim");
        result = result.replaceAll("\\\\to", "→");
        
        // 清理剩余的LaTeX命令
        result = result.replaceAll("\\\\[a-zA-Z]+", "");
        result = result.replaceAll("[{}]", "");
        
        return result.trim();
    }
}
