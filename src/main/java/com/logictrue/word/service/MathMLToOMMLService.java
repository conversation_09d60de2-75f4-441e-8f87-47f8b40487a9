package com.logictrue.word.service;

import lombok.extern.slf4j.Slf4j;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.springframework.stereotype.Service;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.io.ClassPathResource;

import javax.xml.transform.*;
import javax.xml.transform.stream.*;
import java.io.*;
import java.util.HashMap;
import java.util.Map;

/**
 * MathML转OMML服务
 * 将MathML格式的数学公式转换为Office Math Markup Language (OMML)格式
 */
@Slf4j
@Service
public class MathMLToOMMLService {

    // MathML到OMML的元素映射
    private static final Map<String, String> ELEMENT_MAPPING = new HashMap<>();
    
    static {
        // 基本元素映射
        ELEMENT_MAPPING.put("math", "oMath");
        ELEMENT_MAPPING.put("mi", "r");      // 标识符
        ELEMENT_MAPPING.put("mn", "r");      // 数字
        ELEMENT_MAPPING.put("mo", "r");      // 运算符
        ELEMENT_MAPPING.put("mtext", "r");   // 文本
        ELEMENT_MAPPING.put("mrow", "");     // 行，通常不需要特殊标签
        ELEMENT_MAPPING.put("msup", "sSup"); // 上标
        ELEMENT_MAPPING.put("msub", "sSub"); // 下标
        ELEMENT_MAPPING.put("mfrac", "f");   // 分数
        ELEMENT_MAPPING.put("msqrt", "rad"); // 根号
        ELEMENT_MAPPING.put("mroot", "rad"); // n次根号
        ELEMENT_MAPPING.put("munder", "limLow"); // 下限
        ELEMENT_MAPPING.put("mover", "limUpp");  // 上限
        ELEMENT_MAPPING.put("munderover", "limUpp"); // 上下限
    }

    /**
     * 将MathML字符串转换为OMML字符串（使用自定义实现）
     */
    public String convertMathMLToOMML(String mathML) {
        if (StringUtils.isBlank(mathML)) {
            log.warn("MathML字符串为空");
            return "";
        }

        try {
            log.debug("开始转换MathML到OMML（自定义实现）: {}", mathML);

            // 解析MathML文档
            Document mathMLDoc = DocumentHelper.parseText(mathML);
            Element mathElement = mathMLDoc.getRootElement();

            // 创建OMML文档
            Document ommlDoc = DocumentHelper.createDocument();
            Element oMathElement = ommlDoc.addElement("m:oMath");
            oMathElement.addNamespace("m", "http://schemas.openxmlformats.org/officeDocument/2006/math");

            // 转换MathML元素到OMML
            convertElement(mathElement, oMathElement);

            String ommlResult = ommlDoc.asXML();
            log.debug("OMML转换完成（自定义实现）: {}", ommlResult);

            return ommlResult;

        } catch (Exception e) {
            log.error("MathML转OMML失败（自定义实现）: {}", e.getMessage(), e);
            return createFallbackOMML(mathML);
        }
    }

    /**
     * 将MathML字符串转换为OMML字符串（使用XSLT转换）
     */
    public String convertMathMLToOMMLWithXSLT(String mathML) {
        if (StringUtils.isBlank(mathML)) {
            log.warn("MathML字符串为空");
            return "";
        }

        try {
            log.debug("开始转换MathML到OMML（XSLT实现）: {}", mathML);

            // 预处理MathML，确保有正确的命名空间
            String processedMathML = preprocessMathML(mathML);

            // 使用XSLT转换
            String ommlResult = transformWithXSLT(processedMathML);

            log.debug("OMML转换完成（XSLT实现）: {}", ommlResult);

            return ommlResult;

        } catch (Exception e) {
            log.error("MathML转OMML失败（XSLT实现）: {}", e.getMessage(), e);
            return createFallbackOMML(mathML);
        }
    }

    /**
     * 使用XSLT转换MathML到OMML
     */
    private String transformWithXSLT(String mathML) throws Exception {
        // 加载XSLT文件
        ClassPathResource xsltResource = new ClassPathResource("MML2OMML.XSL");

        if (!xsltResource.exists()) {
            throw new FileNotFoundException("XSLT文件不存在: MML2OMML.XSL");
        }

        // 创建转换器
        TransformerFactory factory = TransformerFactory.newInstance();

        try (InputStream xsltStream = xsltResource.getInputStream()) {
            Transformer transformer = factory.newTransformer(new StreamSource(xsltStream));

            // 执行转换
            StringWriter writer = new StringWriter();
            transformer.transform(
                new StreamSource(new StringReader(mathML)),
                new StreamResult(writer)
            );

            return writer.toString();
        }
    }

    /**
     * 预处理MathML，确保有正确的命名空间和格式
     */
    private String preprocessMathML(String mathML) {
        if (StringUtils.isBlank(mathML)) {
            return mathML;
        }

        String processed = mathML.trim();

        // 如果没有XML声明，添加一个
        if (!processed.startsWith("<?xml")) {
            processed = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n" + processed;
        }

        // 确保math元素有正确的命名空间
        if (processed.contains("<math>") && !processed.contains("xmlns")) {
            processed = processed.replace("<math>",
                "<math xmlns=\"http://www.w3.org/1998/Math/MathML\">");
        }

        return processed;
    }

    /**
     * 递归转换MathML元素到OMML元素
     */
    private void convertElement(Element mathMLElement, Element ommlParent) {
        String tagName = mathMLElement.getName();
        log.debug("转换元素: {}", tagName);
        
        switch (tagName) {
            case "math":
                // 根元素，处理子元素
                for (Element child : mathMLElement.elements()) {
                    convertElement(child, ommlParent);
                }
                break;
                
            case "mi":
            case "mn":
            case "mo":
            case "mtext":
                // 文本元素
                convertTextElement(mathMLElement, ommlParent);
                break;
                
            case "mrow":
                // 行元素，直接处理子元素
                for (Element child : mathMLElement.elements()) {
                    convertElement(child, ommlParent);
                }
                break;
                
            case "msup":
                // 上标
                convertSuperscript(mathMLElement, ommlParent);
                break;
                
            case "msub":
                // 下标
                convertSubscript(mathMLElement, ommlParent);
                break;
                
            case "mfrac":
                // 分数
                convertFraction(mathMLElement, ommlParent);
                break;
                
            case "msqrt":
                // 平方根
                convertSquareRoot(mathMLElement, ommlParent);
                break;
                
            case "mroot":
                // n次根
                convertNthRoot(mathMLElement, ommlParent);
                break;
                
            default:
                log.warn("未支持的MathML元素: {}", tagName);
                // 对于不支持的元素，尝试处理其子元素
                for (Element child : mathMLElement.elements()) {
                    convertElement(child, ommlParent);
                }
                break;
        }
    }

    /**
     * 转换文本元素
     */
    private void convertTextElement(Element mathMLElement, Element ommlParent) {
        String text = mathMLElement.getTextTrim();
        if (StringUtils.isNotBlank(text)) {
            Element rElement = ommlParent.addElement("m:r");
            Element tElement = rElement.addElement("m:t");
            tElement.setText(text);
        }
    }

    /**
     * 转换上标
     */
    private void convertSuperscript(Element mathMLElement, Element ommlParent) {
        Element sSupElement = ommlParent.addElement("m:sSup");
        Element eElement = sSupElement.addElement("m:e");
        Element supElement = sSupElement.addElement("m:sup");
        
        // 处理基数和上标
        java.util.List<Element> children = mathMLElement.elements();
        if (children.size() >= 2) {
            convertElement(children.get(0), eElement);
            convertElement(children.get(1), supElement);
        }
    }

    /**
     * 转换下标
     */
    private void convertSubscript(Element mathMLElement, Element ommlParent) {
        Element sSubElement = ommlParent.addElement("m:sSub");
        Element eElement = sSubElement.addElement("m:e");
        Element subElement = sSubElement.addElement("m:sub");
        
        // 处理基数和下标
        java.util.List<Element> children = mathMLElement.elements();
        if (children.size() >= 2) {
            convertElement(children.get(0), eElement);
            convertElement(children.get(1), subElement);
        }
    }

    /**
     * 转换分数
     */
    private void convertFraction(Element mathMLElement, Element ommlParent) {
        Element fElement = ommlParent.addElement("m:f");
        Element numElement = fElement.addElement("m:num");
        Element denElement = fElement.addElement("m:den");
        
        // 处理分子和分母
        java.util.List<Element> children = mathMLElement.elements();
        if (children.size() >= 2) {
            convertElement(children.get(0), numElement);
            convertElement(children.get(1), denElement);
        }
    }

    /**
     * 转换平方根
     */
    private void convertSquareRoot(Element mathMLElement, Element ommlParent) {
        Element radElement = ommlParent.addElement("m:rad");
        Element eElement = radElement.addElement("m:e");
        
        // 处理根号内容
        for (Element child : mathMLElement.elements()) {
            convertElement(child, eElement);
        }
    }

    /**
     * 转换n次根
     */
    private void convertNthRoot(Element mathMLElement, Element ommlParent) {
        Element radElement = ommlParent.addElement("m:rad");
        Element degElement = radElement.addElement("m:deg");
        Element eElement = radElement.addElement("m:e");
        
        // 处理根指数和根号内容
        java.util.List<Element> children = mathMLElement.elements();
        if (children.size() >= 2) {
            convertElement(children.get(0), degElement);
            convertElement(children.get(1), eElement);
        }
    }

    /**
     * 创建备用OMML（当转换失败时使用）
     */
    private String createFallbackOMML(String originalMathML) {
        try {
            Document ommlDoc = DocumentHelper.createDocument();
            Element oMathElement = ommlDoc.addElement("m:oMath");
            oMathElement.addNamespace("m", "http://schemas.openxmlformats.org/officeDocument/2006/math");
            
            Element rElement = oMathElement.addElement("m:r");
            Element tElement = rElement.addElement("m:t");
            tElement.setText("[公式转换失败]");
            
            return ommlDoc.asXML();
        } catch (Exception e) {
            log.error("创建备用OMML失败", e);
            return "";
        }
    }

    /**
     * 检查MathML是否有效
     */
    public boolean isValidMathML(String mathML) {
        if (StringUtils.isBlank(mathML)) {
            return false;
        }
        
        try {
            Document doc = DocumentHelper.parseText(mathML);
            Element root = doc.getRootElement();
            return "math".equals(root.getName()) || mathML.contains("<math");
        } catch (Exception e) {
            log.debug("MathML格式验证失败: {}", e.getMessage());
            return false;
        }
    }
}
