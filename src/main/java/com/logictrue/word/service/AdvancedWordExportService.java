package com.logictrue.word.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.docx4j.Docx4J;
import org.docx4j.openpackaging.exceptions.Docx4JException;
import org.docx4j.openpackaging.packages.WordprocessingMLPackage;
import org.docx4j.openpackaging.parts.WordprocessingML.MainDocumentPart;
import org.springframework.stereotype.Service;
import com.logictrue.word.dto.TableExportRequest;

import java.io.ByteArrayOutputStream;
import java.io.IOException;

/**
 * 高级Word导出服务
 * 使用docx4j实现更强大的Word文档处理功能，特别是数学公式支持
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AdvancedWordExportService {

    private final MathMLToOMMLService mathMLToOMMLService;

    /**
     * 导出表格到Word文档（使用自定义MathML转换）
     */
    public byte[] exportTableToWordWithCustomMath(TableExportRequest request) throws Exception {
        log.info("开始导出Word文档（自定义数学公式转换）: {}", request.getTitle());
        
        try {
            // 创建Word文档包
            WordprocessingMLPackage wordPackage = WordprocessingMLPackage.createPackage();
            MainDocumentPart mainDocumentPart = wordPackage.getMainDocumentPart();
            
            // 添加文档标题
            if (StringUtils.isNotBlank(request.getTitle())) {
                addTitle(mainDocumentPart, request.getTitle());
            }
            
            // 处理表格数据
            if (request.getTableData() != null) {
                processTableWithCustomMath(mainDocumentPart, request.getTableData());
            }
            
            // 转换为字节数组
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            Docx4J.save(wordPackage, outputStream);
            
            log.info("Word文档导出成功（自定义数学公式转换），大小: {} bytes", outputStream.size());
            return outputStream.toByteArray();
            
        } catch (Exception e) {
            log.error("导出Word文档失败（自定义数学公式转换）: {}", e.getMessage(), e);
            throw new RuntimeException("导出Word文档失败: " + e.getMessage(), e);
        }
    }

    /**
     * 导出表格到Word文档（使用XSLT转换）
     */
    public byte[] exportTableToWordWithXSLT(TableExportRequest request) throws Exception {
        log.info("开始导出Word文档（XSLT数学公式转换）: {}", request.getTitle());
        
        try {
            // 创建Word文档包
            WordprocessingMLPackage wordPackage = WordprocessingMLPackage.createPackage();
            MainDocumentPart mainDocumentPart = wordPackage.getMainDocumentPart();
            
            // 添加文档标题
            if (StringUtils.isNotBlank(request.getTitle())) {
                addTitle(mainDocumentPart, request.getTitle());
            }
            
            // 处理表格数据
            if (request.getTableData() != null) {
                processTableWithXSLT(mainDocumentPart, request.getTableData());
            }
            
            // 转换为字节数组
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            Docx4J.save(wordPackage, outputStream);
            
            log.info("Word文档导出成功（XSLT数学公式转换），大小: {} bytes", outputStream.size());
            return outputStream.toByteArray();
            
        } catch (Exception e) {
            log.error("导出Word文档失败（XSLT数学公式转换）: {}", e.getMessage(), e);
            throw new RuntimeException("导出Word文档失败: " + e.getMessage(), e);
        }
    }

    /**
     * 添加文档标题
     */
    private void addTitle(MainDocumentPart mainDocumentPart, String title) throws Exception {
        log.debug("添加文档标题: {}", title);
        
        // 创建标题段落
        org.docx4j.openpackaging.parts.WordprocessingML.ObjectFactory factory = 
            new org.docx4j.openpackaging.parts.WordprocessingML.ObjectFactory();
        
        org.docx4j.wml.P titleParagraph = factory.createP();
        org.docx4j.wml.R titleRun = factory.createR();
        org.docx4j.wml.Text titleText = factory.createText();
        
        titleText.setValue(title);
        titleRun.getContent().add(titleText);
        titleParagraph.getContent().add(titleRun);
        
        // 设置标题样式
        org.docx4j.wml.PPr paragraphProperties = factory.createPPr();
        org.docx4j.wml.Jc justification = factory.createJc();
        justification.setVal(org.docx4j.wml.JcEnumeration.CENTER);
        paragraphProperties.setJc(justification);
        titleParagraph.setPPr(paragraphProperties);
        
        // 设置字体样式
        org.docx4j.wml.RPr runProperties = factory.createRPr();
        org.docx4j.wml.BooleanDefaultTrue bold = factory.createBooleanDefaultTrue();
        runProperties.setB(bold);
        
        org.docx4j.wml.HpsMeasure fontSize = factory.createHpsMeasure();
        fontSize.setVal(java.math.BigInteger.valueOf(32)); // 16pt
        runProperties.setSz(fontSize);
        runProperties.setSzCs(fontSize);
        
        titleRun.setRPr(runProperties);
        
        // 添加到文档
        mainDocumentPart.getContent().add(titleParagraph);
        
        // 添加空行
        org.docx4j.wml.P emptyParagraph = factory.createP();
        mainDocumentPart.getContent().add(emptyParagraph);
    }

    /**
     * 处理表格数据（使用自定义数学公式转换）
     */
    private void processTableWithCustomMath(MainDocumentPart mainDocumentPart, 
                                          TableExportRequest.TableData tableData) throws Exception {
        log.debug("处理表格数据（自定义数学公式转换）");
        
        // 创建表格
        org.docx4j.openpackaging.parts.WordprocessingML.ObjectFactory factory = 
            new org.docx4j.openpackaging.parts.WordprocessingML.ObjectFactory();
        
        org.docx4j.wml.Tbl table = factory.createTbl();
        
        // 处理表头
        if (tableData.getHeaders() != null && !tableData.getHeaders().isEmpty()) {
            for (java.util.List<TableExportRequest.DataCell> headerRow : tableData.getHeaders()) {
                org.docx4j.wml.Tr tableRow = createTableRow(factory, headerRow, true, false);
                table.getContent().add(tableRow);
            }
        }
        
        // 处理数据行
        if (tableData.getDataRows() != null && !tableData.getDataRows().isEmpty()) {
            for (java.util.List<TableExportRequest.DataCell> dataRow : tableData.getDataRows()) {
                org.docx4j.wml.Tr tableRow = createTableRow(factory, dataRow, false, false);
                table.getContent().add(tableRow);
            }
        }
        
        // 添加表格到文档
        mainDocumentPart.getContent().add(table);
    }

    /**
     * 处理表格数据（使用XSLT转换）
     */
    private void processTableWithXSLT(MainDocumentPart mainDocumentPart, 
                                    TableExportRequest.TableData tableData) throws Exception {
        log.debug("处理表格数据（XSLT数学公式转换）");
        
        // 创建表格
        org.docx4j.openpackaging.parts.WordprocessingML.ObjectFactory factory = 
            new org.docx4j.openpackaging.parts.WordprocessingML.ObjectFactory();
        
        org.docx4j.wml.Tbl table = factory.createTbl();
        
        // 处理表头
        if (tableData.getHeaders() != null && !tableData.getHeaders().isEmpty()) {
            for (java.util.List<TableExportRequest.DataCell> headerRow : tableData.getHeaders()) {
                org.docx4j.wml.Tr tableRow = createTableRow(factory, headerRow, true, true);
                table.getContent().add(tableRow);
            }
        }
        
        // 处理数据行
        if (tableData.getDataRows() != null && !tableData.getDataRows().isEmpty()) {
            for (java.util.List<TableExportRequest.DataCell> dataRow : tableData.getDataRows()) {
                org.docx4j.wml.Tr tableRow = createTableRow(factory, dataRow, false, true);
                table.getContent().add(tableRow);
            }
        }
        
        // 添加表格到文档
        mainDocumentPart.getContent().add(table);
    }

    /**
     * 创建表格行
     */
    private org.docx4j.wml.Tr createTableRow(
            org.docx4j.openpackaging.parts.WordprocessingML.ObjectFactory factory,
            java.util.List<TableExportRequest.DataCell> cells,
            boolean isHeader,
            boolean useXSLT) throws Exception {
        
        org.docx4j.wml.Tr tableRow = factory.createTr();
        
        for (TableExportRequest.DataCell cell : cells) {
            org.docx4j.wml.Tc tableCell = factory.createTc();
            
            // 创建段落
            org.docx4j.wml.P paragraph = factory.createP();
            
            // 处理单元格内容
            if (Boolean.TRUE.equals(cell.getHasMath()) && StringUtils.isNotBlank(cell.getMathML())) {
                // 处理数学公式
                addMathFormula(factory, paragraph, cell.getMathML(), useXSLT);
            } else {
                // 处理普通文本
                addTextContent(factory, paragraph, cell.getContent(), isHeader);
            }
            
            tableCell.getContent().add(paragraph);
            tableRow.getContent().add(tableCell);
        }
        
        return tableRow;
    }

    /**
     * 添加数学公式到段落
     */
    private void addMathFormula(org.docx4j.openpackaging.parts.WordprocessingML.ObjectFactory factory,
                              org.docx4j.wml.P paragraph, String mathML, boolean useXSLT) {
        try {
            log.debug("添加数学公式，使用XSLT: {}", useXSLT);
            
            // 根据选择使用不同的转换方法
            String omml;
            if (useXSLT) {
                omml = mathMLToOMMLService.convertMathMLToOMMLWithXSLT(mathML);
            } else {
                omml = mathMLToOMMLService.convertMathMLToOMML(mathML);
            }
            
            if (StringUtils.isNotBlank(omml)) {
                // 这里需要将OMML字符串转换为docx4j可以处理的对象
                // 由于docx4j的复杂性，这里先用文本表示
                org.docx4j.wml.R run = factory.createR();
                org.docx4j.wml.Text text = factory.createText();
                text.setValue("[数学公式: " + (useXSLT ? "XSLT转换" : "自定义转换") + "]");
                run.getContent().add(text);
                paragraph.getContent().add(run);
                
                log.debug("数学公式添加成功，转换方法: {}", useXSLT ? "XSLT" : "自定义");
            } else {
                // 转换失败，使用原始内容
                addTextContent(factory, paragraph, "[公式转换失败]", false);
            }
            
        } catch (Exception e) {
            log.error("添加数学公式失败: {}", e.getMessage(), e);
            addTextContent(factory, paragraph, "[公式错误]", false);
        }
    }

    /**
     * 添加文本内容到段落
     */
    private void addTextContent(org.docx4j.openpackaging.parts.WordprocessingML.ObjectFactory factory,
                              org.docx4j.wml.P paragraph, String content, boolean isHeader) {
        org.docx4j.wml.R run = factory.createR();
        org.docx4j.wml.Text text = factory.createText();
        
        text.setValue(content != null ? content : "");
        run.getContent().add(text);
        
        // 如果是表头，设置粗体
        if (isHeader) {
            org.docx4j.wml.RPr runProperties = factory.createRPr();
            org.docx4j.wml.BooleanDefaultTrue bold = factory.createBooleanDefaultTrue();
            runProperties.setB(bold);
            run.setRPr(runProperties);
        }
        
        paragraph.getContent().add(run);
    }
}
