package com.logictrue.word.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import com.logictrue.word.dto.TableExportRequest;

/**
 * 高级Word导出服务
 * 提供两种不同的数学公式转换方法测试
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AdvancedWordExportService {

    private final MathMLToOMMLService mathMLToOMMLService;
    private final WordExportService wordExportService;

    /**
     * 导出表格到Word文档（使用自定义MathML转换）
     */
    public byte[] exportTableToWordWithCustomMath(TableExportRequest request) throws Exception {
        log.info("开始导出Word文档（自定义数学公式转换）: {}", request.getTitle());
        
        try {
            // 预处理数学公式 - 使用自定义转换
            preprocessMathFormulas(request, false);
            
            // 使用现有的WordExportService导出
            return wordExportService.exportTableToWord(request);
            
        } catch (Exception e) {
            log.error("导出Word文档失败（自定义数学公式转换）: {}", e.getMessage(), e);
            throw new RuntimeException("导出Word文档失败: " + e.getMessage(), e);
        }
    }

    /**
     * 导出表格到Word文档（使用XSLT转换）
     */
    public byte[] exportTableToWordWithXSLT(TableExportRequest request) throws Exception {
        log.info("开始导出Word文档（XSLT数学公式转换）: {}", request.getTitle());
        
        try {
            // 预处理数学公式 - 使用XSLT转换
            preprocessMathFormulas(request, true);
            
            // 使用现有的WordExportService导出
            return wordExportService.exportTableToWord(request);
            
        } catch (Exception e) {
            log.error("导出Word文档失败（XSLT数学公式转换）: {}", e.getMessage(), e);
            throw new RuntimeException("导出Word文档失败: " + e.getMessage(), e);
        }
    }

    /**
     * 预处理数学公式
     */
    private void preprocessMathFormulas(TableExportRequest request, boolean useXSLT) {
        if (request.getTableData() == null || request.getTableData().getDataRows() == null) {
            return;
        }

        log.debug("预处理数学公式，使用XSLT: {}", useXSLT);

        for (java.util.List<TableExportRequest.DataCell> row : request.getTableData().getDataRows()) {
            for (TableExportRequest.DataCell cell : row) {
                if (Boolean.TRUE.equals(cell.getHasMath()) && StringUtils.isNotBlank(cell.getMathML())) {
                    try {
                        String omml;
                        if (useXSLT) {
                            omml = mathMLToOMMLService.convertMathMLToOMMLWithXSLT(cell.getMathML());
                        } else {
                            omml = mathMLToOMMLService.convertMathMLToOMML(cell.getMathML());
                        }
                        
                        // 将转换结果记录到日志中
                        log.debug("数学公式转换完成 - 方法: {}, 原始MathML长度: {}, OMML长度: {}", 
                                useXSLT ? "XSLT" : "自定义", 
                                cell.getMathML().length(), 
                                omml != null ? omml.length() : 0);
                        
                        // 可以在这里将OMML结果存储到cell中，供后续使用
                        // 暂时只做转换测试
                        
                    } catch (Exception e) {
                        log.error("数学公式转换失败: {}", e.getMessage(), e);
                    }
                }
            }
        }
    }
}
