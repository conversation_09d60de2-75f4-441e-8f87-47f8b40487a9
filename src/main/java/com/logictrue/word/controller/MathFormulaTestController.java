package com.logictrue.word.controller;

import com.logictrue.word.dto.TableExportRequest;
import com.logictrue.word.service.AdvancedWordExportService;
import com.logictrue.word.service.MathMLToOMMLService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 数学公式测试控制器
 * 提供两种不同的数学公式转换实现测试接口
 */
@Slf4j
@RestController
@RequestMapping("/mathTest")
@RequiredArgsConstructor
public class MathFormulaTestController {

    private final MathMLToOMMLService mathMLToOMMLService;
    private final AdvancedWordExportService advancedWordExportService;

    /**
     * 测试MathML转OMML转换（自定义实现）
     */
    @PostMapping("/convertCustom")
    public ResponseEntity<Map<String, Object>> testCustomConversion(@RequestBody Map<String, String> request) {
        log.info("测试自定义MathML转OMML转换");

        Map<String, Object> response = new HashMap<>();

        try {
            String mathML = request.get("mathML");
            if (mathML == null || mathML.trim().isEmpty()) {
                response.put("success", false);
                response.put("error", "MathML不能为空");
                return ResponseEntity.badRequest().body(response);
            }

            log.debug("输入MathML: {}", mathML);

            // 使用自定义实现转换
            String omml = mathMLToOMMLService.convertMathMLToOMML(mathML);

            response.put("success", true);
            response.put("method", "自定义实现");
            response.put("inputMathML", mathML);
            response.put("outputOMML", omml);
            response.put("timestamp", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));

            log.info("自定义转换成功，OMML长度: {}", omml.length());

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("自定义转换失败: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("error", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 测试MathML转OMML转换（XSLT实现）
     */
    @PostMapping("/convertXSLT")
    public ResponseEntity<Map<String, Object>> testXSLTConversion(@RequestBody Map<String, String> request) {
        log.info("测试XSLT MathML转OMML转换");

        Map<String, Object> response = new HashMap<>();

        try {
            String mathML = request.get("mathML");
            if (mathML == null || mathML.trim().isEmpty()) {
                response.put("success", false);
                response.put("error", "MathML不能为空");
                return ResponseEntity.badRequest().body(response);
            }

            log.debug("输入MathML: {}", mathML);

            // 使用XSLT实现转换
            String omml = mathMLToOMMLService.convertMathMLToOMMLWithXSLT(mathML);

            response.put("success", true);
            response.put("method", "XSLT实现");
            response.put("inputMathML", mathML);
            response.put("outputOMML", omml);
            response.put("timestamp", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));

            log.info("XSLT转换成功，OMML长度: {}", omml.length());

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("XSLT转换失败: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("error", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 比较两种转换方法的结果
     */
    @PostMapping("/compare")
    public ResponseEntity<Map<String, Object>> compareConversions(@RequestBody Map<String, String> request) {
        log.info("比较两种MathML转OMML转换方法");

        Map<String, Object> response = new HashMap<>();

        try {
            String mathML = request.get("mathML");
            if (mathML == null || mathML.trim().isEmpty()) {
                response.put("success", false);
                response.put("error", "MathML不能为空");
                return ResponseEntity.badRequest().body(response);
            }

            log.debug("比较转换，输入MathML: {}", mathML);

            // 自定义实现转换
            long startTime1 = System.currentTimeMillis();
            String customOMML = mathMLToOMMLService.convertMathMLToOMML(mathML);
            long customTime = System.currentTimeMillis() - startTime1;

            // XSLT实现转换
            long startTime2 = System.currentTimeMillis();
            String xsltOMML = mathMLToOMMLService.convertMathMLToOMMLWithXSLT(mathML);
            long xsltTime = System.currentTimeMillis() - startTime2;

            response.put("success", true);
            response.put("inputMathML", mathML);
            Map<String, Object> customResult = new HashMap<>();
            customResult.put("omml", customOMML);
            customResult.put("time", customTime + "ms");
            customResult.put("length", customOMML.length());
            response.put("customResult", customResult);

            Map<String, Object> xsltResult = new HashMap<>();
            xsltResult.put("omml", xsltOMML);
            xsltResult.put("time", xsltTime + "ms");
            xsltResult.put("length", xsltOMML.length());
            response.put("xsltResult", xsltResult);

            Map<String, Object> comparison = new HashMap<>();
            comparison.put("resultsEqual", customOMML.equals(xsltOMML));
            comparison.put("customFaster", customTime < xsltTime);
            comparison.put("timeDifference", Math.abs(customTime - xsltTime) + "ms");
            response.put("comparison", comparison);
            response.put("timestamp", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));

            log.info("转换比较完成 - 自定义: {}ms, XSLT: {}ms", customTime, xsltTime);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("转换比较失败: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("error", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 导出Word文档（使用自定义数学公式转换）
     */
    @PostMapping("/exportWordCustom")
    public ResponseEntity<byte[]> exportWordWithCustomMath(@RequestBody TableExportRequest request) {
        log.info("导出Word文档（自定义数学公式转换）: {}", request.getTitle());

        try {
            // 如果没有提供测试数据，创建一些
            if (request.getTableData() == null ||
                request.getTableData().getDataRows() == null ||
                request.getTableData().getDataRows().isEmpty()) {
                request.setTableData(createTestTableData());
            }

            byte[] wordBytes = advancedWordExportService.exportTableToWordWithCustomMath(request);

            // 生成文件名
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
            String fileName = String.format("%s_自定义转换_%s.docx",
                request.getTitle() != null ? request.getTitle() : "数学公式测试", timestamp);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentDispositionFormData("attachment", fileName);
            headers.setContentLength(wordBytes.length);

            log.info("Word文档导出成功（自定义转换），文件大小: {} bytes", wordBytes.length);

            return ResponseEntity.ok()
                .headers(headers)
                .body(wordBytes);

        } catch (Exception e) {
            log.error("导出Word文档失败（自定义转换）: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * 导出Word文档（使用XSLT数学公式转换）
     */
    @PostMapping("/exportWordXSLT")
    public ResponseEntity<byte[]> exportWordWithXSLT(@RequestBody TableExportRequest request) {
        log.info("导出Word文档（XSLT数学公式转换）: {}", request.getTitle());

        try {
            // 如果没有提供测试数据，创建一些
            if (request.getTableData() == null ||
                request.getTableData().getDataRows() == null ||
                request.getTableData().getDataRows().isEmpty()) {
                request.setTableData(createTestTableData());
            }

            byte[] wordBytes = advancedWordExportService.exportTableToWordWithXSLT(request);

            // 生成文件名
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
            String fileName = String.format("%s_XSLT转换_%s.docx",
                request.getTitle() != null ? request.getTitle() : "数学公式测试", timestamp);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentDispositionFormData("attachment", fileName);
            headers.setContentLength(wordBytes.length);

            log.info("Word文档导出成功（XSLT转换），文件大小: {} bytes", wordBytes.length);

            return ResponseEntity.ok()
                .headers(headers)
                .body(wordBytes);

        } catch (Exception e) {
            log.error("导出Word文档失败（XSLT转换）: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    /**
     * 创建测试表格数据
     */
    private TableExportRequest.TableData createTestTableData() {
        TableExportRequest.TableData tableData = new TableExportRequest.TableData();

        // 创建表头
        List<List<TableExportRequest.DataCell>> headers = new ArrayList<>();
        List<TableExportRequest.DataCell> headerRow = new ArrayList<>();
        headerRow.add(createDataCell("项目", false, null, 120, 50));
        headerRow.add(createDataCell("公式", false, null, 200, 50));
        headerRow.add(createDataCell("说明", false, null, 150, 50));
        headers.add(headerRow);
        tableData.setDataRows(headers);

        // 创建数据行
        List<List<TableExportRequest.DataCell>> dataRows = new ArrayList<>();

        // 第一行：勾股定理
        List<TableExportRequest.DataCell> row1 = new ArrayList<>();
        row1.add(createDataCell("勾股定理", false, null, 120, 50));
        row1.add(createDataCell("$a^2 + b^2 = c^2$", true,
            "<math><msup><mi>a</mi><mn>2</mn></msup><mo>+</mo><msup><mi>b</mi><mn>2</mn></msup><mo>=</mo><msup><mi>c</mi><mn>2</mn></msup></math>",
            200, 50));
        row1.add(createDataCell("直角三角形边长关系", false, null, 150, 50));
        dataRows.add(row1);

        // 第二行：二次方程
        List<TableExportRequest.DataCell> row2 = new ArrayList<>();
        row2.add(createDataCell("二次方程解", false, null, 120, 50));
        row2.add(createDataCell("$x = \\frac{-b \\pm \\sqrt{b^2-4ac}}{2a}$", true,
            "<math><mi>x</mi><mo>=</mo><mfrac><mrow><mo>-</mo><mi>b</mi><mo>±</mo><msqrt><mrow><msup><mi>b</mi><mn>2</mn></msup><mo>-</mo><mn>4</mn><mi>a</mi><mi>c</mi></mrow></msqrt></mrow><mrow><mn>2</mn><mi>a</mi></mrow></mfrac></math>",
            200, 50));
        row2.add(createDataCell("一元二次方程求根公式", false, null, 150, 50));
        dataRows.add(row2);

        // 第三行：圆面积
        List<TableExportRequest.DataCell> row3 = new ArrayList<>();
        row3.add(createDataCell("圆面积", false, null, 120, 50));
        row3.add(createDataCell("$A = \\pi r^2$", true,
            "<math><mi>A</mi><mo>=</mo><mi>π</mi><msup><mi>r</mi><mn>2</mn></msup></math>",
            200, 50));
        row3.add(createDataCell("圆的面积计算公式", false, null, 150, 50));
        dataRows.add(row3);

        tableData.setDataRows(dataRows);
        return tableData;
    }

    /**
     * 创建数据单元格
     */
    private TableExportRequest.DataCell createDataCell(String content, Boolean hasMath, String mathML,
                                                     Integer width, Integer height) {
        TableExportRequest.DataCell cell = new TableExportRequest.DataCell();
        cell.setContent(content);
        cell.setHasMath(hasMath);
        cell.setMathML(mathML);
        cell.setWidth(width);
        cell.setHeight(height);
        return cell;
    }

    /**
     * 测试自定义转换Word导出 (GET请求直接下载)
     */
    @GetMapping("/downloadCustom")
    public ResponseEntity<byte[]> downloadWordWithCustomMath() {
        log.info("GET请求：导出Word文档（自定义数学公式转换）");

        try {
            // 创建测试请求
            TableExportRequest request = new TableExportRequest();
            request.setTitle("自定义转换测试文档");
            request.setTableData(createTestTableData());

            byte[] wordBytes = advancedWordExportService.exportTableToWordWithCustomMath(request);

            // 生成文件名
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
            String fileName = String.format("自定义转换测试_%s.docx", timestamp);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentDispositionFormData("attachment", fileName);
            headers.setContentLength(wordBytes.length);

            log.info("Word文档导出成功（自定义转换GET），文件大小: {} bytes", wordBytes.length);

            return ResponseEntity.ok()
                .headers(headers)
                .body(wordBytes);

        } catch (Exception e) {
            log.error("GET导出Word文档失败（自定义转换）: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * 测试XSLT转换Word导出 (GET请求直接下载)
     */
    @GetMapping("/downloadXSLT")
    public ResponseEntity<byte[]> downloadWordWithXSLT() {
        log.info("GET请求：导出Word文档（XSLT数学公式转换）");

        try {
            // 创建测试请求
            TableExportRequest request = new TableExportRequest();
            request.setTitle("XSLT转换测试文档");
            request.setTableData(createTestTableData());

            byte[] wordBytes = advancedWordExportService.exportTableToWordWithXSLT(request);

            // 生成文件名
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
            String fileName = String.format("XSLT转换测试_%s.docx", timestamp);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentDispositionFormData("attachment", fileName);
            headers.setContentLength(wordBytes.length);

            log.info("Word文档导出成功（XSLT转换GET），文件大小: {} bytes", wordBytes.length);

            return ResponseEntity.ok()
                .headers(headers)
                .body(wordBytes);

        } catch (Exception e) {
            log.error("GET导出Word文档失败（XSLT转换）: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * 快速测试转换 (GET请求)
     */
    @GetMapping("/quickTest")
    public ResponseEntity<Map<String, Object>> quickTest() {
        log.info("GET请求：快速测试数学公式转换");

        Map<String, Object> response = new HashMap<>();

        try {
            // 测试用的MathML
            String testMathML = "<math><msup><mi>a</mi><mn>2</mn></msup><mo>+</mo><msup><mi>b</mi><mn>2</mn></msup><mo>=</mo><msup><mi>c</mi><mn>2</mn></msup></math>";

            // 自定义转换测试
            long startTime1 = System.currentTimeMillis();
            String customOMML = mathMLToOMMLService.convertMathMLToOMML(testMathML);
            long customTime = System.currentTimeMillis() - startTime1;

            // XSLT转换测试
            long startTime2 = System.currentTimeMillis();
            String xsltOMML = mathMLToOMMLService.convertMathMLToOMMLWithXSLT(testMathML);
            long xsltTime = System.currentTimeMillis() - startTime2;

            response.put("success", true);
            response.put("testMathML", testMathML);
            Map<String, Object> customResult = new HashMap<>();
            customResult.put("omml", customOMML.length() > 100 ? customOMML.substring(0, 100) + "..." : customOMML);
            customResult.put("time", customTime + "ms");
            customResult.put("length", customOMML.length());
            response.put("customResult", customResult);

            Map<String, Object> xsltResult = new HashMap<>();
            xsltResult.put("omml", xsltOMML.length() > 100 ? xsltOMML.substring(0, 100) + "..." : xsltOMML);
            xsltResult.put("time", xsltTime + "ms");
            xsltResult.put("length", xsltOMML.length());
            response.put("xsltResult", xsltResult);
            response.put("timestamp", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));

            log.info("快速测试完成 - 自定义: {}ms, XSLT: {}ms", customTime, xsltTime);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("快速测试失败: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("error", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 获取测试用的MathML示例
     */
    @GetMapping("/sampleMathML")
    public ResponseEntity<Map<String, Object>> getSampleMathML() {
        Map<String, Object> response = new HashMap<>();

        List<Map<String, String>> samples = new ArrayList<>();

        Map<String, String> sample1 = new HashMap<>();
        sample1.put("name", "简单等式");
        sample1.put("latex", "$x = 5$");
        sample1.put("mathml", "<math><mi>x</mi><mo>=</mo><mn>5</mn></math>");
        samples.add(sample1);

        Map<String, String> sample2 = new HashMap<>();
        sample2.put("name", "勾股定理");
        sample2.put("latex", "$a^2 + b^2 = c^2$");
        sample2.put("mathml", "<math><msup><mi>a</mi><mn>2</mn></msup><mo>+</mo><msup><mi>b</mi><mn>2</mn></msup><mo>=</mo><msup><mi>c</mi><mn>2</mn></msup></math>");
        samples.add(sample2);

        Map<String, String> sample3 = new HashMap<>();
        sample3.put("name", "分数");
        sample3.put("latex", "$\\frac{a}{b} = \\frac{c}{d}$");
        sample3.put("mathml", "<math><mfrac><mi>a</mi><mi>b</mi></mfrac><mo>=</mo><mfrac><mi>c</mi><mi>d</mi></mfrac></math>");
        samples.add(sample3);

        response.put("success", true);
        response.put("samples", samples);
        response.put("count", samples.size());

        return ResponseEntity.ok(response);
    }

}
