<template>
  <div class="math-test-container">
    <div class="header">
      <h2>数学公式转换测试</h2>
      <p>测试LaTeX转MathML功能</p>
    </div>

    <div class="test-section">
      <h3>LaTeX输入测试</h3>
      <div class="input-group">
        <label>输入LaTeX公式：</label>
        <el-input
          v-model="latexInput"
          placeholder="例如：$x^2 + y^2 = z^2$"
          @input="handleLatexInput"
        />
      </div>

      <div class="result-group">
        <h4>转换结果：</h4>
        <div class="result-item">
          <label>是否包含数学公式：</label>
          <span :class="{ 'has-math': containsMathResult }">
            {{ containsMathResult ? '是' : '否' }}
          </span>
        </div>
        
        <div class="result-item" v-if="mathMLResult">
          <label>MathML输出：</label>
          <div class="mathml-output">
            <pre>{{ mathMLResult }}</pre>
          </div>
        </div>

        <div class="result-item" v-if="mathMLResult">
          <label>渲染预览：</label>
          <div class="math-preview" ref="mathPreview" v-html="mathMLResult"></div>
        </div>
      </div>
    </div>

    <div class="test-section">
      <h3>预设测试用例</h3>
      <div class="test-cases">
        <div 
          v-for="(testCase, index) in testCases" 
          :key="index"
          class="test-case"
          @click="selectTestCase(testCase)"
        >
          <div class="test-case-title">{{ testCase.title }}</div>
          <div class="test-case-latex">{{ testCase.latex }}</div>
        </div>
      </div>
    </div>

    <div class="test-section">
      <h3>批量测试</h3>
      <el-button type="primary" @click="runBatchTest" :loading="batchTesting">
        运行批量测试
      </el-button>
      
      <div v-if="batchResults.length > 0" class="batch-results">
        <h4>批量测试结果：</h4>
        <div 
          v-for="(result, index) in batchResults" 
          :key="index"
          class="batch-result-item"
          :class="{ 'success': result.success, 'error': !result.success }"
        >
          <div class="result-title">{{ result.title }}</div>
          <div class="result-latex">LaTeX: {{ result.latex }}</div>
          <div class="result-status">
            状态: {{ result.success ? '成功' : '失败' }}
            <span v-if="!result.success" class="error-msg">{{ result.error }}</span>
          </div>
          <div v-if="result.mathML" class="result-mathml">
            MathML: {{ result.mathML.substring(0, 100) }}...
          </div>
        </div>
      </div>
    </div>

    <div class="test-section">
      <h3>表格数据测试</h3>
      <el-button type="success" @click="testTableData" :loading="tableTesting">
        测试表格数据处理
      </el-button>
      
      <div v-if="tableTestResult" class="table-test-result">
        <h4>表格测试结果：</h4>
        <pre>{{ JSON.stringify(tableTestResult, null, 2) }}</pre>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'MathTest',
  data() {
    return {
      latexInput: '$x^2 + y^2 = z^2$',
      mathMLResult: '',
      containsMathResult: false,
      batchTesting: false,
      tableTesting: false,
      batchResults: [],
      tableTestResult: null,
      mathUtils: null,
      
      testCases: [
        { title: '简单等式', latex: '$x = 5$' },
        { title: '平方公式', latex: '$x^2 + y^2 = z^2$' },
        { title: '分数', latex: '$\\frac{a}{b} = \\frac{c}{d}$' },
        { title: '根号', latex: '$\\sqrt{x + y}$' },
        { title: '上下标', latex: '$x_1^2 + x_2^2$' },
        { title: '希腊字母', latex: '$\\alpha + \\beta = \\gamma$' },
        { title: '求和', latex: '$\\sum_{i=1}^{n} x_i$' },
        { title: '积分', latex: '$\\int_0^1 x dx$' },
        { title: '复杂公式', latex: '$\\frac{x^2 + \\sqrt{y}}{z - 1}$' },
        { title: '显示公式', latex: '$$E = mc^2$$' }
      ]
    }
  },
  
  async mounted() {
    await this.initMathUtils()
    await this.handleLatexInput()
  },
  
  methods: {
    async initMathUtils() {
      try {
        const mathFormulaUtils = await import('@/utils/math-formula-utils.js')
        this.mathUtils = mathFormulaUtils.default
        console.log('数学工具初始化成功')
      } catch (error) {
        console.error('数学工具初始化失败:', error)
        this.$message.error('数学工具初始化失败')
      }
    },
    
    async handleLatexInput() {
      if (!this.mathUtils) {
        return
      }
      
      try {
        // 检测是否包含数学公式
        this.containsMathResult = this.mathUtils.containsMath(this.latexInput)
        
        if (this.containsMathResult) {
          // 转换为MathML
          this.mathMLResult = await this.mathUtils.latexToMathML(this.latexInput)
        } else {
          this.mathMLResult = ''
        }
      } catch (error) {
        console.error('LaTeX转换失败:', error)
        this.mathMLResult = ''
      }
    },
    
    selectTestCase(testCase) {
      this.latexInput = testCase.latex
      this.handleLatexInput()
    },
    
    async runBatchTest() {
      if (!this.mathUtils) {
        this.$message.error('数学工具未初始化')
        return
      }
      
      this.batchTesting = true
      this.batchResults = []
      
      try {
        for (const testCase of this.testCases) {
          try {
            const containsMath = this.mathUtils.containsMath(testCase.latex)
            let mathML = ''
            
            if (containsMath) {
              mathML = await this.mathUtils.latexToMathML(testCase.latex)
            }
            
            this.batchResults.push({
              title: testCase.title,
              latex: testCase.latex,
              success: true,
              containsMath,
              mathML
            })
          } catch (error) {
            this.batchResults.push({
              title: testCase.title,
              latex: testCase.latex,
              success: false,
              error: error.message
            })
          }
        }
        
        this.$message.success('批量测试完成')
      } catch (error) {
        console.error('批量测试失败:', error)
        this.$message.error('批量测试失败')
      } finally {
        this.batchTesting = false
      }
    },
    
    async testTableData() {
      if (!this.mathUtils) {
        this.$message.error('数学工具未初始化')
        return
      }
      
      this.tableTesting = true
      
      try {
        // 创建测试表格数据
        const testTableData = {
          headers: [
            [
              { content: '项目', rowspan: 1, colspan: 1 },
              { content: '公式', rowspan: 1, colspan: 1 },
              { content: '结果', rowspan: 1, colspan: 1 }
            ]
          ],
          dataRows: [
            [
              { content: '面积公式', hasMath: false },
              { content: '$A = \\pi r^2$', hasMath: false },
              { content: '$A = 3.14 \\times 5^2$', hasMath: false }
            ],
            [
              { content: '二次方程', hasMath: false },
              { content: '$ax^2 + bx + c = 0$', hasMath: false },
              { content: '$x = \\frac{-b \\pm \\sqrt{b^2-4ac}}{2a}$', hasMath: false }
            ]
          ]
        }
        
        // 处理表格数据
        this.tableTestResult = await this.mathUtils.processTableData(testTableData)
        
        this.$message.success('表格数据测试完成')
      } catch (error) {
        console.error('表格数据测试失败:', error)
        this.$message.error('表格数据测试失败')
      } finally {
        this.tableTesting = false
      }
    }
  }
}
</script>

<style scoped>
.math-test-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.header {
  text-align: center;
  margin-bottom: 30px;
}

.test-section {
  margin-bottom: 40px;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
}

.input-group {
  margin-bottom: 20px;
}

.input-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: bold;
}

.result-group {
  margin-top: 20px;
}

.result-item {
  margin-bottom: 15px;
}

.result-item label {
  font-weight: bold;
  margin-right: 10px;
}

.has-math {
  color: #67c23a;
  font-weight: bold;
}

.mathml-output {
  background: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  font-family: monospace;
  font-size: 12px;
  max-height: 200px;
  overflow-y: auto;
}

.math-preview {
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
  min-height: 40px;
}

.test-cases {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 15px;
}

.test-case {
  padding: 15px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s;
}

.test-case:hover {
  border-color: #409eff;
  background: #f0f9ff;
}

.test-case-title {
  font-weight: bold;
  margin-bottom: 8px;
  color: #303133;
}

.test-case-latex {
  font-family: monospace;
  color: #606266;
  font-size: 14px;
}

.batch-results {
  margin-top: 20px;
}

.batch-result-item {
  padding: 10px;
  margin-bottom: 10px;
  border-radius: 4px;
  border-left: 4px solid #ddd;
}

.batch-result-item.success {
  border-left-color: #67c23a;
  background: #f0f9ff;
}

.batch-result-item.error {
  border-left-color: #f56c6c;
  background: #fef0f0;
}

.result-title {
  font-weight: bold;
  margin-bottom: 5px;
}

.result-latex, .result-status, .result-mathml {
  font-size: 14px;
  margin-bottom: 3px;
}

.error-msg {
  color: #f56c6c;
  margin-left: 10px;
}

.table-test-result {
  margin-top: 20px;
}

.table-test-result pre {
  background: #f5f5f5;
  padding: 15px;
  border-radius: 4px;
  font-size: 12px;
  max-height: 400px;
  overflow-y: auto;
}
</style>
