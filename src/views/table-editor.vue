<template>
  <div class="table-editor-page">
    <!-- 控制面板 -->
    <div class="control-panel">
<!--      <h3>表格控制面板</h3>-->

      <!-- 表格尺寸和实时信息并排显示 -->
      <div class="control-section">
        <div class="control-row-split">
          <!-- 左侧：表格整体尺寸 -->
          <div class="control-column">
            <h4>表格整体尺寸</h4>
            <div class="control-row-compact">
              <div class="control-item-compact">
                <label>宽度:</label>
                <div class="input-with-icon">
                  <input
                    type="text"
                    v-model="tableWidthInput"
                    @input="debounceUpdateTableWidth"
                    placeholder="100vw"
                    class="dimension-input width-input"
                  >
                  <span class="input-icon">📏</span>
                </div>
              </div>
              <div class="control-item-compact">
                <label>高度:</label>
                <div class="input-with-icon">
                  <input
                    type="text"
                    v-model="tableHeightInput"
                    @input="debounceUpdateTableHeight"
                    placeholder="auto"
                    class="dimension-input height-input"
                  >
                  <span class="input-icon">📐</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 右侧：实时信息 -->
          <div class="control-column">
            <h4>实时信息</h4>
            <div class="info-display-compact">
              <div>宽度: {{ currentTableWidth }}px</div>
              <div>高度: {{ currentTableHeight }}px</div>
              <div>单元格: {{ totalCells }}个</div>
            </div>
          </div>
        </div>
      </div>

      <div class="control-section">
        <div class="control-buttons">
          <button @click="showUniformSizeDialog" class="uniform-size-button">
            <span class="button-icon">📐</span>
            统一单元格尺寸
          </button>
          <button @click="addBlankRow" class="add-row-button">
            <span class="button-icon">➕</span>
            添加空白行
          </button>
          <button @click="showMathHelpDialog" class="math-help-button">
            <span class="button-icon">∑</span>
            数学公式帮助
          </button>
          <button @click="resetTable" class="reset-button">重置表格</button>
          <button @click="downloadTable" class="download-button">下载HTML</button>
          <button @click="exportToWord" class="export-word-button">导出Word</button>
          <button @click="testWordExport" class="test-export-button">测试GET导出</button>
          <button @click="testPostExport" class="test-post-button">测试POST导出</button>
          <button @click="testMathFormulas" class="test-math-button">测试数学公式</button>
          <button @click="addTestRows" class="test-button">添加测试行</button>
        </div>
      </div>
    </div>

    <!-- 表格容器 -->
    <div class="table-wrapper">
      <div class="table-container" ref="tableContainer" :style="tableContainerStyle">
        <div class="table-scroll-container">
          <table ref="editableTable" :style="tableStyle" @contextmenu="handleContextMenu">
          <!-- 表头第一行 - 不可编辑 -->
          <tr>
            <td class="header-cell" rowspan="2" :title="'检查工序名称'">
              检查工序名称
            </td>
            <td class="header-cell" rowspan="2" :title="'检查项目及技术条件'">
              检查项目及技术条件
            </td>
            <td class="header-cell" rowspan="2" :title="'实际检查结果'">
              实际检查结果
            </td>
            <td class="header-cell" colspan="2" :title="'完工'">
              完工
            </td>
            <td class="header-cell" rowspan="2" :title="'操作员'">
              操作员
            </td>
            <td class="header-cell" rowspan="2" :title="'班组长'">
              班组长
            </td>
            <td class="header-cell" rowspan="2" :title="'检验员'">
              检验员
            </td>
          </tr>
          <!-- 表头第二行 - 不可编辑 -->
          <tr>
            <td class="header-cell" :title="'月'">
              月
            </td>
            <td class="header-cell" :title="'日'">
              日
            </td>
          </tr>
          <!-- 数据行 - 可编辑 -->
          <tr v-for="(row, rowIndex) in dataRows" :key="rowIndex">
            <td
              v-for="(cell, cellIndex) in row"
              :key="cellIndex"
              class="editable-cell"
              :class="{ 'has-math': cell.hasMath }"
              :title="cell.content"
              @click="handleCellClick(rowIndex, cellIndex, $event)"
              @dblclick="handleCellDoubleClick(rowIndex, cellIndex, $event)"
              @mousedown="handleCellMouseDown(rowIndex, cellIndex, $event)"
              @blur="finishEdit(rowIndex, cellIndex)"
              @keydown.enter="finishEdit(rowIndex, cellIndex)"
              @keydown.esc="cancelEdit(rowIndex, cellIndex)"
              :contenteditable="cell.isEditing"
              @input="updateCellContent(rowIndex, cellIndex, $event)"
              @compositionstart="handleCompositionStart(rowIndex, cellIndex)"
              @compositionend="handleCompositionEnd(rowIndex, cellIndex, $event)"
            >
              <template v-if="!cell.isEditing">{{ cell.content }}</template>
            </td>
          </tr>
        </table>
        </div>
      </div>
    </div>

    <!-- 右键菜单 -->
    <div
      v-if="contextMenuVisible"
      class="context-menu"
      :style="{ left: contextMenuX + 'px', top: contextMenuY + 'px' }"
    >
      <div
        class="context-menu-item"
        @click="handleRowHeightClick"
        @mousedown.stop
      >
        调整此行高度
      </div>
      <div
        class="context-menu-item"
        @click="handleColumnWidthClick"
        @mousedown.stop
      >
        调整此列宽度
      </div>
      <div class="context-menu-divider"></div>
      <div
        v-if="isDataRow"
        class="context-menu-item delete-item"
        @click="handleDeleteRowClick"
        @mousedown.stop
      >
        删除此行
      </div>
    </div>

    <!-- 行高调整对话框 -->
    <div v-if="rowHeightDialogVisible" class="dialog-overlay" @click="closeRowHeightDialog">
      <div class="dialog" @click.stop>
        <h3>调整行高度</h3>
        <div class="dialog-content">
          <label>当前行高度: {{ currentRowHeight }}px</label>
          <input
            type="number"
            v-model="newRowHeight"
            :min="minCellHeight"
            placeholder="输入新的行高度(px)"
            @keydown.enter="applyRowHeight"
            @keydown.esc="closeRowHeightDialog"
            ref="rowHeightInput"
          >
        </div>
        <div class="dialog-buttons">
          <button @click="closeRowHeightDialog" class="btn-cancel">取消</button>
          <button @click="applyRowHeight" class="btn-confirm">确定</button>
        </div>
      </div>
    </div>

    <!-- 列宽调整对话框 -->
    <div v-if="columnWidthDialogVisible" class="dialog-overlay" @click="closeColumnWidthDialog">
      <div class="dialog" @click.stop>
        <h3>调整列宽度</h3>
        <div class="dialog-content">
          <label>当前列宽度: {{ currentColumnWidth }}px</label>
          <input
            type="number"
            v-model="newColumnWidth"
            :min="minCellWidth"
            placeholder="输入新的列宽度(px)"
            @keydown.enter="applyColumnWidth"
            @keydown.esc="closeColumnWidthDialog"
            ref="columnWidthInput"
          >
        </div>
        <div class="dialog-buttons">
          <button @click="closeColumnWidthDialog" class="btn-cancel">取消</button>
          <button @click="applyColumnWidth" class="btn-confirm">确定</button>
        </div>
      </div>
    </div>

    <!-- 统一尺寸设置对话框 -->
    <div v-if="uniformSizeDialogVisible" class="dialog-overlay" @click="closeUniformSizeDialog">
      <div class="dialog uniform-size-dialog" @click.stop>
        <h3>统一单元格尺寸</h3>
        <div class="dialog-content">
          <div class="uniform-size-section">
            <h4>当前设置</h4>
            <div class="current-settings">
              <div class="setting-item">
                <span class="setting-label">统一宽度:</span>
                <span class="setting-value">{{ uniformCellWidth }}px</span>
              </div>
              <div class="setting-item">
                <span class="setting-label">统一高度:</span>
                <span class="setting-value">{{ uniformCellHeight }}px</span>
              </div>
            </div>
          </div>

          <div class="uniform-size-section">
            <h4>新的设置</h4>
            <div class="uniform-inputs">
              <div class="uniform-input-group">
                <label>统一宽度:</label>
                <div class="input-with-icon">
                  <input
                    type="number"
                    v-model="newUniformWidth"
                    :min="minCellWidth"
                    placeholder="120"
                    class="dimension-input uniform-dialog-input"
                    @keydown.enter="handleApplyUniformSize"
                    @keydown.esc="closeUniformSizeDialog"
                    ref="uniformWidthInput"
                  >
                  <span class="input-unit">px</span>
                </div>
              </div>

              <div class="uniform-input-group">
                <label>统一高度:</label>
                <div class="input-with-icon">
                  <input
                    type="number"
                    v-model="newUniformHeight"
                    :min="minCellHeight"
                    placeholder="50"
                    class="dimension-input uniform-dialog-input"
                    @keydown.enter="handleApplyUniformSize"
                    @keydown.esc="closeUniformSizeDialog"
                  >
                  <span class="input-unit">px</span>
                </div>
              </div>
            </div>
          </div>

          <div class="uniform-size-section">
            <div class="quick-presets">
              <h4>快速预设</h4>
              <div class="preset-buttons">
                <button @click="applyPreset('small')" class="preset-btn small-preset">
                  小尺寸 (80×30)
                </button>
                <button @click="applyPreset('medium')" class="preset-btn medium-preset">
                  中尺寸 (120×50)
                </button>
                <button @click="applyPreset('large')" class="preset-btn large-preset">
                  大尺寸 (160×70)
                </button>
              </div>
            </div>
          </div>
        </div>

        <div class="dialog-buttons">
          <button @click="closeUniformSizeDialog" class="btn-cancel">取消</button>
          <button @click="handleApplyUniformSize" class="btn-confirm">应用设置</button>
        </div>
      </div>
    </div>

    <!-- 数学公式帮助对话框 -->
    <div v-if="mathHelpDialogVisible" class="dialog-overlay" @click="closeMathHelpDialog">
      <div class="dialog math-help-dialog" @click.stop>
        <h3>数学公式输入帮助</h3>
        <div class="dialog-content">
          <div class="math-help-section">
            <h4>基本语法</h4>
            <div class="math-examples">
              <div class="math-example">
                <div class="example-label">行内公式：</div>
                <div class="example-input">$E = mc^2$</div>
              </div>
              <div class="math-example">
                <div class="example-label">显示公式：</div>
                <div class="example-input">$$\\int_0^\\infty e^{-x^2} dx$$</div>
              </div>
            </div>
          </div>

          <div class="math-help-section">
            <h4>常用符号</h4>
            <div class="symbol-grid">
              <div class="symbol-item">
                <span class="symbol-display">α β γ</span>
                <span class="symbol-latex">\\alpha \\beta \\gamma</span>
              </div>
              <div class="symbol-item">
                <span class="symbol-display">∑ ∏ ∫</span>
                <span class="symbol-latex">\\sum \\prod \\int</span>
              </div>
              <div class="symbol-item">
                <span class="symbol-display">√ ∞ ±</span>
                <span class="symbol-latex">\\sqrt{} \\infty \\pm</span>
              </div>
            </div>
          </div>

          <div class="math-help-section">
            <h4>快速示例</h4>
            <div class="quick-examples">
              <button @click="insertQuickExample('$x^2 + y^2 = r^2$')" class="example-btn">
                平方和
              </button>
              <button @click="insertQuickExample('$\\frac{a}{b}$')" class="example-btn">
                分数
              </button>
              <button @click="insertQuickExample('$\\sqrt{x}$')" class="example-btn">
                平方根
              </button>
              <button @click="insertQuickExample('$\\sum_{i=1}^n x_i$')" class="example-btn">
                求和
              </button>
            </div>
          </div>
        </div>

        <div class="dialog-buttons">
          <button @click="closeMathHelpDialog" class="btn-confirm">关闭</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>


export default {
  name: 'TableEditor',
  data() {
    return {
      // 表格尺寸控制
      tableWidthInput: '1600px',
      tableHeightInput: '300px',
      uniformCellWidth: 120,
      uniformCellHeight: 50,

      // 实时信息
      currentTableWidth: 0,
      currentTableHeight: 0,
      totalCells: 0,

      // 右键菜单状态
      contextMenuVisible: false,
      contextMenuX: 0,
      contextMenuY: 0,
      currentContextCell: null,
      isDataRow: false, // 是否为数据行（非表头）
      currentRowIndex: -1,
      currentColumnIndex: -1,

      // 行高调整对话框
      rowHeightDialogVisible: false,
      currentRowHeight: 0,
      newRowHeight: 0,

      // 列宽调整对话框
      columnWidthDialogVisible: false,
      currentColumnWidth: 0,
      newColumnWidth: 0,

      // 统一尺寸设置对话框
      uniformSizeDialogVisible: false,
      newUniformWidth: 120,
      newUniformHeight: 50,

      // 最小尺寸限制
      minCellWidth: 20,
      minCellHeight: 20,

      // 表格数据行（不包括表头）
      dataRows: [
        // 初始化一行空数据，8个单元格
        Array(8).fill(null).map(() => ({
          content: '',
          isEditing: false,
          originalContent: '',
          hasMath: false // 是否包含数学公式
        }))
      ],

      // MathJax相关
      mathJaxReady: false,
      mathJaxRetryAttempted: false, // 是否已尝试过强制重新初始化
      mathHelpDialogVisible: false,
      currentEditingCell: null, // 当前正在编辑的单元格引用

      // 中文输入法状态
      isComposing: false, // 是否正在进行中文输入

      // 点击状态跟踪
      clickTimer: null, // 点击延迟定时器
      clickCount: 0, // 点击计数

      // 防抖定时器
      debounceTimers: {},

      // 初始状态保存
      initialTableState: null
    }
  },
  computed: {
    tableContainerStyle() {
      const style = {
        width: this.tableWidthInput
      }

      // 如果设置了具体高度，则应用到容器
      if (this.tableHeightInput !== 'auto') {
        style.height = this.tableHeightInput
        style.maxHeight = this.tableHeightInput
      }

      return style
    },
    tableStyle() {
      return {
        width: '100%',
        height: 'auto'
      }
    }
  },
  mounted() {
    this.initializeTable()
    this.saveInitialState()
    this.updateTableInfo()
    this.setupEventListeners()
    this.initializeMathJax()

  },
  beforeDestroy() {
    this.removeEventListeners()
    this.clearAllDebounceTimers()

    // 清理点击定时器
    if (this.clickTimer) {
      clearTimeout(this.clickTimer)
      this.clickTimer = null
    }
  },
  methods: {
    // 初始化表格
    initializeTable() {
      this.$nextTick(() => {

        // 计算并设置单元格数量
        this.calculateTotalCells()

        // 更新表格信息
        this.updateTableInfo()
      })
    },

    // 保存初始状态
    saveInitialState() {
      this.$nextTick(() => {
        const table = this.$refs.editableTable
        if (table) {
          this.initialTableState = {
            width: this.tableWidthInput,
            height: this.tableHeightInput,
            cellStyles: this.getCellStyles(table)
          }
        }
      })
    },

    // 获取所有单元格样式
    getCellStyles(table) {
      const cells = table.querySelectorAll('td')
      const styles = []
      cells.forEach(cell => {
        const rect = cell.getBoundingClientRect()
        styles.push({
          width: rect.width,
          height: rect.height,
          minWidth: cell.style.minWidth || '',
          minHeight: cell.style.minHeight || ''
        })
      })
      return styles
    },

    // 计算单元格总数
    calculateTotalCells() {
      // 表头单元格数：第一行8个，第二行2个，总共10个
      const headerCells = 10
      // 数据行单元格数：每行8个单元格
      const dataCells = this.dataRows.length * 8
      this.totalCells = headerCells + dataCells
      console.log('计算单元格总数:', this.totalCells, '(表头:', headerCells, '+ 数据:', dataCells, ')')
    },

    // 更新表格信息
    updateTableInfo() {
      this.$nextTick(() => {
        const container = this.$refs.tableContainer
        const table = this.$refs.editableTable
        if (container && table) {
          const containerRect = container.getBoundingClientRect()
          const tableRect = table.getBoundingClientRect()

          this.currentTableWidth = Math.round(containerRect.width)
          this.currentTableHeight = Math.round(containerRect.height)

          console.log('表格尺寸信息:', {
            containerWidth: containerRect.width,
            containerHeight: containerRect.height,
            tableWidth: tableRect.width,
            tableHeight: tableRect.height,
            needsScroll: tableRect.height > containerRect.height
          })
        }
      })
    },

    // 设置事件监听器
    setupEventListeners() {
      window.addEventListener('resize', this.handleWindowResize)
      // 使用 mousedown 而不是 click，避免与菜单项点击冲突
      document.addEventListener('mousedown', this.handleDocumentClick)
    },

    // 移除事件监听器
    removeEventListeners() {
      window.removeEventListener('resize', this.handleWindowResize)
      document.removeEventListener('mousedown', this.handleDocumentClick)
    },

    // 窗口大小改变处理
    handleWindowResize() {
      this.debounce('windowResize', () => {
        this.updateTableInfo()
      }, 300)
    },

    // 防抖函数
    debounce(key, func, delay) {
      if (this.debounceTimers[key]) {
        clearTimeout(this.debounceTimers[key])
      }
      this.debounceTimers[key] = setTimeout(() => {
        func()
        delete this.debounceTimers[key]
      }, delay)
    },

    // 清除所有防抖定时器
    clearAllDebounceTimers() {
      Object.values(this.debounceTimers).forEach(timer => clearTimeout(timer))
      this.debounceTimers = {}
    },

    // 防抖更新表格宽度
    debounceUpdateTableWidth() {
      this.debounce('tableWidth', () => {
        this.updateTableInfo()
      }, 300)
    },

    // 防抖更新表格高度
    debounceUpdateTableHeight() {
      this.debounce('tableHeight', () => {
        this.updateTableInfo()
      }, 300)
    },

    // 处理右键菜单
    handleContextMenu(e) {
      e.preventDefault()

      const cell = e.target.closest('td')
      if (!cell) {
        console.log('未找到单元格')
        return
      }

      console.log('右键点击单元格', cell)

      this.currentContextCell = cell
      this.contextMenuX = e.pageX
      this.contextMenuY = e.pageY
      this.contextMenuVisible = true

      // 判断是否为数据行（非表头）
      this.isDataRow = cell.classList.contains('editable-cell')

      // 计算当前单元格的行列索引
      this.calculateCellPosition(cell)

      // 获取当前行高和列宽
      this.getCurrentRowHeight()
      this.getCurrentColumnWidth()

      console.log('右键菜单数据', {
        contextMenuVisible: this.contextMenuVisible,
        isDataRow: this.isDataRow,
        currentRowHeight: this.currentRowHeight,
        currentColumnWidth: this.currentColumnWidth,
        currentRowIndex: this.currentRowIndex,
        currentColumnIndex: this.currentColumnIndex
      })

      // 调整菜单位置避免超出屏幕
      this.$nextTick(() => {
        this.adjustContextMenuPosition()
      })
    },

    // 计算单元格位置
    calculateCellPosition(cell) {
      const table = this.$refs.editableTable
      if (!table) {
        console.log('表格引用不存在')
        return
      }

      const rows = Array.from(table.rows)
      const rowIndex = rows.findIndex(row => Array.from(row.cells).includes(cell))
      this.currentRowIndex = rowIndex

      console.log('计算得到行索引', rowIndex)

      if (rowIndex >= 0) {
        // 计算列索引（考虑colspan）
        let columnIndex = 0
        const row = rows[rowIndex]
        const cells = Array.from(row.cells)
        const cellIndex = cells.indexOf(cell)

        // 累加前面所有单元格的colspan
        for (let i = 0; i < cellIndex; i++) {
          const colspan = parseInt(cells[i].getAttribute('colspan') || '1')
          columnIndex += colspan
        }

        this.currentColumnIndex = columnIndex

        console.log('列索引计算详情:', {
          rowIndex: rowIndex,
          cellIndex: cellIndex,
          calculatedColumnIndex: columnIndex,
          cellText: cell.textContent.trim(),
          cellColspan: cell.getAttribute('colspan') || '1'
        })
      } else {
        this.currentColumnIndex = 0
        console.log('行索引无效，设置列索引为0')
      }
    },

    // 获取当前行高度
    getCurrentRowHeight() {
      if (this.currentRowIndex >= 0) {
        const table = this.$refs.editableTable
        if (table && table.rows[this.currentRowIndex]) {
          const row = table.rows[this.currentRowIndex]
          const rect = row.getBoundingClientRect()
          this.currentRowHeight = Math.round(rect.height)
          console.log('获取行高度', this.currentRowHeight, 'px')
        } else {
          this.currentRowHeight = 50 // 默认值
          console.log('使用默认行高度', this.currentRowHeight, 'px')
        }
      } else {
        this.currentRowHeight = 50 // 默认值
        console.log('行索引无效，使用默认行高度', this.currentRowHeight, 'px')
      }
    },

    // 获取当前列宽度
    getCurrentColumnWidth() {
      if (this.currentContextCell) {
        const rect = this.currentContextCell.getBoundingClientRect()
        this.currentColumnWidth = Math.round(rect.width)
        console.log('获取列宽度', this.currentColumnWidth, 'px')
      } else {
        this.currentColumnWidth = 120 // 默认值
        console.log('单元格无效，使用默认列宽度', this.currentColumnWidth, 'px')
      }
    },

    // 调整右键菜单位置
    adjustContextMenuPosition() {
      const menu = document.querySelector('.context-menu')
      if (!menu) return

      const menuRect = menu.getBoundingClientRect()
      const windowWidth = window.innerWidth
      const windowHeight = window.innerHeight

      if (this.contextMenuX + menuRect.width > windowWidth) {
        this.contextMenuX = windowWidth - menuRect.width - 10
      }

      if (this.contextMenuY + menuRect.height > windowHeight) {
        this.contextMenuY = windowHeight - menuRect.height - 10
      }
    },

    // 处理文档点击（关闭菜单）
    handleDocumentClick(e) {
      // 如果右键菜单不可见，直接返回
      if (!this.contextMenuVisible) {
        return
      }

      console.log('文档点击事件，目标:', e.target.className)

      // 检查点击是否在右键菜单内
      if (e.target.closest('.context-menu')) {
        console.log('点击在右键菜单内，不关闭菜单')
        return
      }

      // 关闭右键菜单
      console.log('关闭右键菜单')
      this.contextMenuVisible = false
    },

    // 显示行高调整对话框
    showRowHeightDialog() {
      console.log('=== 显示行高调整对话框被调用 ===')
      console.log('当前数据:', {
        currentRowHeight: this.currentRowHeight,
        currentRowIndex: this.currentRowIndex,
        contextMenuVisible: this.contextMenuVisible,
        rowHeightDialogVisible: this.rowHeightDialogVisible
      })

      // 先关闭右键菜单
      this.contextMenuVisible = false

      // 设置默认值
      this.newRowHeight = this.currentRowHeight || 50

      // 显示对话框
      this.rowHeightDialogVisible = true

      console.log('设置后的状态:', {
        contextMenuVisible: this.contextMenuVisible,
        rowHeightDialogVisible: this.rowHeightDialogVisible,
        newRowHeight: this.newRowHeight
      })

      this.$nextTick(() => {
        console.log('nextTick中检查input引用:', this.$refs.rowHeightInput)
        if (this.$refs.rowHeightInput) {
          this.$refs.rowHeightInput.focus()
          this.$refs.rowHeightInput.select()
        }
      })
    },

    // 显示列宽调整对话框
    showColumnWidthDialog() {
      console.log('=== 显示列宽调整对话框被调用 ===')
      console.log('当前数据:', {
        currentColumnWidth: this.currentColumnWidth,
        currentColumnIndex: this.currentColumnIndex,
        contextMenuVisible: this.contextMenuVisible,
        columnWidthDialogVisible: this.columnWidthDialogVisible
      })

      // 先关闭右键菜单
      this.contextMenuVisible = false

      // 设置默认值
      this.newColumnWidth = this.currentColumnWidth || 120

      // 显示对话框
      this.columnWidthDialogVisible = true

      console.log('设置后的状态:', {
        contextMenuVisible: this.contextMenuVisible,
        columnWidthDialogVisible: this.columnWidthDialogVisible,
        newColumnWidth: this.newColumnWidth
      })

      this.$nextTick(() => {
        console.log('nextTick中检查input引用:', this.$refs.columnWidthInput)
        if (this.$refs.columnWidthInput) {
          this.$refs.columnWidthInput.focus()
          this.$refs.columnWidthInput.select()
        }
      })
    },

    // 关闭行高调整对话框
    closeRowHeightDialog() {
      this.rowHeightDialogVisible = false
    },

    // 关闭列宽调整对话框
    closeColumnWidthDialog() {
      this.columnWidthDialogVisible = false
    },

    // 显示统一尺寸设置对话框
    showUniformSizeDialog() {
      console.log('显示统一尺寸对话框')
      this.newUniformWidth = this.uniformCellWidth
      this.newUniformHeight = this.uniformCellHeight
      this.uniformSizeDialogVisible = true

      console.log('对话框状态:', {
        visible: this.uniformSizeDialogVisible,
        currentWidth: this.uniformCellWidth,
        currentHeight: this.uniformCellHeight,
        newWidth: this.newUniformWidth,
        newHeight: this.newUniformHeight
      })

      this.$nextTick(() => {
        if (this.$refs.uniformWidthInput) {
          this.$refs.uniformWidthInput.focus()
          this.$refs.uniformWidthInput.select()
        }
      })
    },

    // 关闭统一尺寸设置对话框
    closeUniformSizeDialog() {
      console.log('关闭统一尺寸对话框')
      this.uniformSizeDialogVisible = false
    },

    // 处理应用统一尺寸按钮点击
    handleApplyUniformSize() {
      console.log('=== 应用设置按钮被点击 ===')
      this.applyUniformSize()
    },

    // 处理单元格鼠标按下
    handleCellMouseDown(rowIndex, cellIndex, event) {
      // 确保数据行存在
      if (!this.dataRows[rowIndex]) {
        this.$set(this.dataRows, rowIndex, Array(8).fill(null).map(() => ({
          content: '',
          isEditing: false,
          originalContent: ''
        })))
      }

      const cell = this.dataRows[rowIndex][cellIndex]
      if (cell && cell.isEditing) {
        // 如果已经在编辑状态，阻止事件冒泡，让用户可以自由设置光标位置
        event.stopPropagation()
        console.log('编辑状态下的鼠标点击，允许设置光标位置')
      }
    },

    // 处理单元格点击
    handleCellClick(rowIndex, cellIndex, event) {
      console.log('单元格点击:', rowIndex, cellIndex)

      // 确保数据行存在
      if (!this.dataRows[rowIndex]) {
        this.$set(this.dataRows, rowIndex, Array(8).fill(null).map(() => ({
          content: '',
          isEditing: false,
          originalContent: ''
        })))
      }

      const cell = this.dataRows[rowIndex][cellIndex]
      if (cell) {
        if (!cell.isEditing) {
          // 如果不在编辑状态，开始编辑
          this.startEdit(rowIndex, cellIndex, true)
        } else {
          // 如果已经在编辑状态，延迟处理以区分单击和双击
          this.clickCount++

          if (this.clickTimer) {
            clearTimeout(this.clickTimer)
          }

          this.clickTimer = setTimeout(() => {
            if (this.clickCount === 1) {
              // 单击：不做任何处理，让浏览器自然处理光标位置
              console.log('编辑状态下单击，保持光标位置')
            }
            this.clickCount = 0
          }, 200) // 200ms内的多次点击视为双击
        }
      }
    },

    // 处理单元格双击
    handleCellDoubleClick(rowIndex, cellIndex, event) {
      console.log('单元格双击:', rowIndex, cellIndex)

      // 清除单击定时器
      if (this.clickTimer) {
        clearTimeout(this.clickTimer)
        this.clickTimer = null
      }
      this.clickCount = 0

      const cell = this.dataRows[rowIndex] && this.dataRows[rowIndex][cellIndex]
      if (cell && cell.isEditing) {
        // 如果已经在编辑状态，双击时选中全部内容
        const tableCell = this.getCellElement(rowIndex, cellIndex)
        if (tableCell && cell.content) {
          setTimeout(() => {
            try {
              const range = document.createRange()
              const selection = window.getSelection()
              range.selectNodeContents(tableCell)
              selection.removeAllRanges()
              selection.addRange(range)
              console.log('双击选中全部内容')
            } catch (error) {
              console.warn('双击选中文本失败:', error)
            }
          }, 10)
        }
      }
    },

    // 开始编辑单元格
    startEdit(rowIndex, cellIndex, selectAll = true) {
      console.log('开始编辑单元格:', rowIndex, cellIndex, '选中全部:', selectAll)

      const cell = this.dataRows[rowIndex][cellIndex]
      if (cell) {
        // 保存原始内容用于取消编辑
        cell.originalContent = cell.content
        cell.isEditing = true

        // 下一帧设置DOM内容并聚焦到单元格
        this.$nextTick(() => {
          const tableCell = this.getCellElement(rowIndex, cellIndex)
          if (tableCell) {
            // 设置DOM内容为当前单元格内容
            tableCell.textContent = cell.content
            tableCell.focus()

            // 根据参数决定是否选中所有文本
            if (selectAll && cell.content) {
              // 延迟一点时间再选中，避免与鼠标点击冲突
              setTimeout(() => {
                try {
                  const range = document.createRange()
                  const selection = window.getSelection()
                  range.selectNodeContents(tableCell)
                  selection.removeAllRanges()
                  selection.addRange(range)
                } catch (error) {
                  console.warn('选中文本失败:', error)
                }
              }, 10)
            } else if (!cell.content) {
              // 如果内容为空，设置光标到开始位置
              const range = document.createRange()
              const selection = window.getSelection()
              range.setStart(tableCell, 0)
              range.setEnd(tableCell, 0)
              selection.removeAllRanges()
              selection.addRange(range)
            }
          }
        })
      }
    },

    // 完成编辑
    finishEdit(rowIndex, cellIndex) {
      console.log('完成编辑单元格:', rowIndex, cellIndex)

      if (this.dataRows[rowIndex] && this.dataRows[rowIndex][cellIndex]) {
        const cell = this.dataRows[rowIndex][cellIndex]

        // 获取实际的DOM内容
        const tableCell = this.getCellElement(rowIndex, cellIndex)
        if (tableCell) {
          const newContent = tableCell.textContent || ''

          // 先更新内容
          cell.content = newContent

          // 然后设置为非编辑状态
          cell.isEditing = false

          // 检测是否包含数学公式
          cell.hasMath = this.containsMath(cell.content)

          console.log('保存内容:', cell.content, '包含数学公式:', cell.hasMath)

          // 如果包含数学公式，渲染MathJax
          if (cell.hasMath) {
            this.$nextTick(async () => {
              try {
                await this.renderMathJax(tableCell)
                console.log('MathJax渲染完成')
              } catch (error) {
                console.error('MathJax渲染失败:', error)
              }
            })
          }
        } else {
          // 如果无法获取DOM元素，直接设置为非编辑状态
          cell.isEditing = false
        }
      }
    },

    // 取消编辑
    cancelEdit(rowIndex, cellIndex) {
      console.log('取消编辑单元格:', rowIndex, cellIndex)

      if (this.dataRows[rowIndex] && this.dataRows[rowIndex][cellIndex]) {
        const cell = this.dataRows[rowIndex][cellIndex]
        // 恢复原始内容
        cell.content = cell.originalContent
        cell.isEditing = false

        // 更新DOM内容
        const tableCell = this.getCellElement(rowIndex, cellIndex)
        if (tableCell) {
          tableCell.textContent = cell.content
        }
      }
    },

    // 更新单元格内容
    updateCellContent(rowIndex, cellIndex, event) {
      // 在编辑状态下，我们不更新Vue的响应式数据
      // 这样可以避免DOM重新渲染导致光标位置丢失
      // 内容会在finishEdit或compositionend时同步

      // 如果正在进行中文输入，也不做任何处理
      if (this.isComposing) {
        return
      }

      // 在编辑状态下，让DOM自然更新，不触发Vue响应式系统
      console.log('输入内容变化，但不更新Vue数据以保持光标位置')
    },

    // 处理中文输入开始
    handleCompositionStart(rowIndex, cellIndex) {
      console.log('中文输入开始:', rowIndex, cellIndex)
      this.isComposing = true
    },

    // 处理中文输入结束
    handleCompositionEnd(rowIndex, cellIndex, event) {
      console.log('中文输入结束:', rowIndex, cellIndex)
      this.isComposing = false

      // 中文输入结束后，不需要立即更新Vue数据
      // 内容会在finishEdit时统一同步
      console.log('中文输入完成，内容将在编辑完成时同步')
    },

    // 获取单元格DOM元素
    getCellElement(rowIndex, cellIndex) {
      const table = this.$refs.editableTable
      if (table) {
        // 跳过前两行表头，从第三行开始是数据行
        const targetRow = table.rows[rowIndex + 2]
        if (targetRow && targetRow.cells[cellIndex]) {
          return targetRow.cells[cellIndex]
        }
      }
      return null
    },

    // 初始化MathJax
    async initializeMathJax() {
      try {
        console.log('开始初始化MathJax...')

        // 设置MathJax配置
        window.MathJax = {
          tex: {
            inlineMath: [['$', '$'], ['\\(', '\\)']],
            displayMath: [['$$', '$$'], ['\\[', '\\]']]
          },
          chtml: {
            // 本地字体路径
            fontURL: '/fonts/mathjax'          }
        }

        // 从CDN加载MathJax
        /*const script = document.createElement('script')
        script.src = 'https://polyfill.io/v3/polyfill.min.js?features=es6'
        document.head.appendChild(script)*/

        const mathJaxScript = document.createElement('script')
        mathJaxScript.id = 'MathJax-script'
        mathJaxScript.async = true
        //mathJaxScript.src = 'https://cdn.jsdelivr.net/npm/mathjax@4/tex-mml-chtml.js'
        mathJaxScript.src = '/js/mathjax/tex-mml-chtml-mathjax-newcm.js'

        mathJaxScript.onload = () => {
          this.mathJaxReady = true
          console.log('MathJax初始化成功')
        }

        mathJaxScript.onerror = () => {
          console.error('MathJax加载失败')
          this.mathJaxReady = false
        }

        document.head.appendChild(mathJaxScript)

      } catch (error) {
        console.error('MathJax初始化失败:', error)
        this.mathJaxReady = false
      }
    },

    // 渲染MathJax公式
    async renderMathJax(element) {
      try {
        if (!this.mathJaxReady || !window.MathJax) {
          console.warn('MathJax未就绪，等待加载...')
          // 等待MathJax加载完成
          let attempts = 0
          while ((!window.MathJax || !this.mathJaxReady) && attempts < 50) {
            await new Promise(resolve => setTimeout(resolve, 100))
            attempts++
          }
        }

        if (window.MathJax && window.MathJax.typesetPromise) {
          console.log('开始渲染MathJax公式...')
          await window.MathJax.typesetPromise([element])
          console.log('MathJax渲染完成')
        } else {
          console.warn('MathJax渲染方法不可用')
        }
      } catch (error) {
        console.error('MathJax渲染错误:', error)
      }
    },

    // 检测内容是否包含数学公式
    containsMath(content) {
      if (!content) return false

      const mathPatterns = [
        /\$.*?\$/,           // 行内公式 $...$
        /\$\$.*?\$\$/,       // 显示公式 $$...$$
        /\\\(.*?\\\)/,       // 行内公式 \(...\)
        /\\\[.*?\\\]/,       // 显示公式 \[...\]
        /\\begin\{.*?\}.*?\\end\{.*?\}/,  // 环境公式
        /\\[a-zA-Z]+/        // LaTeX命令
      ]

      return mathPatterns.some(pattern => pattern.test(content))
    },

    // 应用统一尺寸设置
    applyUniformSize() {
      const width = Math.max(this.minCellWidth, parseInt(this.newUniformWidth) || this.minCellWidth)
      const height = Math.max(this.minCellHeight, parseInt(this.newUniformHeight) || this.minCellHeight)

      console.log('开始应用统一尺寸:', { width, height })

      // 更新统一尺寸值
      this.uniformCellWidth = width
      this.uniformCellHeight = height

      // 应用到所有单元格
      const table = this.$refs.editableTable
      if (table) {
        const cells = table.querySelectorAll('td')
        console.log('找到单元格数量:', cells.length)

        cells.forEach((cell, index) => {
          cell.style.width = `${width}px`
          cell.style.minWidth = `${width}px`
          cell.style.height = `${height}px`
          cell.style.minHeight = `${height}px`
        })

        console.log('统一尺寸应用完成')
        this.updateTableInfo()
      } else {
        console.error('未找到表格元素')
      }

      this.closeUniformSizeDialog()
    },

    // 应用预设尺寸
    applyPreset(size) {
      console.log('点击预设按钮:', size)

      const presets = {
        small: { width: 80, height: 30 },
        medium: { width: 120, height: 50 },
        large: { width: 160, height: 70 }
      }

      const preset = presets[size]
      if (preset) {
        this.newUniformWidth = preset.width
        this.newUniformHeight = preset.height

        console.log('应用预设:', size, preset, {
          newWidth: this.newUniformWidth,
          newHeight: this.newUniformHeight
        })
      } else {
        console.error('未找到预设:', size)
      }
    },

    // 处理行高点击
    handleRowHeightClick(e) {
      console.log('=== 行高菜单项被点击 ===')
      e.preventDefault()
      e.stopPropagation()
      this.showRowHeightDialog()
    },

    // 处理列宽点击
    handleColumnWidthClick(e) {
      console.log('=== 列宽菜单项被点击 ===')
      e.preventDefault()
      e.stopPropagation()
      this.showColumnWidthDialog()
    },

    // 处理删除行点击
    handleDeleteRowClick(e) {
      console.log('=== 删除行菜单项被点击 ===')
      e.preventDefault()
      e.stopPropagation()
      this.deleteCurrentRow()
    },

    // 删除当前行
    deleteCurrentRow() {
      if (this.isDataRow && this.currentRowIndex >= 2) {
        // 计算在dataRows数组中的索引（减去2行表头）
        const dataRowIndex = this.currentRowIndex - 2

        if (dataRowIndex >= 0 && dataRowIndex < this.dataRows.length) {
          // 确认删除
          if (confirm('确定要删除这一行吗？此操作不可撤销。')) {
            this.dataRows.splice(dataRowIndex, 1)

            console.log('已删除行:', dataRowIndex, '当前总行数:', this.dataRows.length)

            // 更新统计信息
            this.calculateTotalCells()
            this.updateTableInfo()

            // 关闭右键菜单
            this.contextMenuVisible = false
          }
        }
      }
    },

    // 添加空白行
    addBlankRow() {
      const newRow = Array(8).fill(null).map(() => ({
        content: '',
        isEditing: false,
        originalContent: '',
        hasMath: false
      }))

      this.dataRows.push(newRow)

      console.log('已添加1行空白数据，当前总行数:', this.dataRows.length)
      this.calculateTotalCells()
      this.updateTableInfo()

      // 滚动到新添加的行
      this.$nextTick(() => {
        this.scrollToNewRow()
      })
    },

    // 滚动到新添加的行
    scrollToNewRow() {
      const scrollContainer = document.querySelector('.table-scroll-container')
      const table = this.$refs.editableTable

      if (scrollContainer && table) {
        // 获取最后一行的位置
        const lastRowIndex = this.dataRows.length - 1 + 2 // +2 因为前面有两行表头
        const lastRow = table.rows[lastRowIndex]

        if (lastRow) {
          // 平滑滚动到最后一行
          lastRow.scrollIntoView({
            behavior: 'smooth',
            block: 'center'
          })

          console.log('滚动到新行:', lastRowIndex)
        }
      }
    },

    // 添加测试行（用于测试滚动功能）
    addTestRows() {
      // 添加5行测试数据到数据结构，包含一些数学公式示例
      const mathExamples = [
        '$E = mc^2$',
        '$\\int_0^\\infty e^{-x^2} dx = \\frac{\\sqrt{\\pi}}{2}$',
        '$\\sum_{n=1}^\\infty \\frac{1}{n^2} = \\frac{\\pi^2}{6}$',
        '$\\lim_{x \\to 0} \\frac{\\sin x}{x} = 1$',
        '$\\sqrt{a^2 + b^2}$'
      ]

      for (let i = 0; i < 5; i++) {
        const newRow = Array(8).fill(null).map((_, j) => {
          let content = `测试数据 ${this.dataRows.length + i + 1}-${j + 1}`

          // 在某些单元格中添加数学公式示例
          if (j === 2 && i < mathExamples.length) {
            content = mathExamples[i]
          }

          return {
            content: content,
            isEditing: false,
            originalContent: '',
            hasMath: this.containsMath(content)
          }
        })
        this.dataRows.push(newRow)
      }

      console.log('已添加5行测试数据（包含数学公式），当前总行数:', this.dataRows.length)
      this.calculateTotalCells()
      this.updateTableInfo()

      // 渲染新添加行中的数学公式
      this.$nextTick(() => {
        this.renderAllMathJax()
      })
    },

    // 渲染所有MathJax公式
    async renderAllMathJax() {
      if (this.mathJaxReady) {
        const table = this.$refs.editableTable
        if (table) {
          try {
            await this.renderMathJax(table)
            console.log('所有MathJax公式渲染完成')
          } catch (error) {
            console.error('渲染所有MathJax公式失败:', error)
          }
        }
      }
    },

    // 显示数学公式帮助对话框
    showMathHelpDialog() {
      this.mathHelpDialogVisible = true

      // 渲染帮助对话框中的数学公式
      this.$nextTick(async () => {
        const dialog = document.querySelector('.math-help-dialog')
        if (dialog && this.mathJaxReady) {
          try {
            await this.renderMathJax(dialog)
          } catch (error) {
            console.error('帮助对话框MathJax渲染失败:', error)
          }
        }
      })
    },

    // 关闭数学公式帮助对话框
    closeMathHelpDialog() {
      this.mathHelpDialogVisible = false
    },

    // 插入快速示例
    insertQuickExample(latex) {
      // 这里可以实现将公式插入到当前编辑的单元格中
      // 暂时只是复制到剪贴板
      if (navigator.clipboard) {
        navigator.clipboard.writeText(latex).then(() => {
          alert(`公式 "${latex}" 已复制到剪贴板，您可以粘贴到单元格中`)
        }).catch(() => {
          prompt('请复制以下公式:', latex)
        })
      } else {
        prompt('请复制以下公式:', latex)
      }
    },





    // 应用行高调整
    applyRowHeight() {
      const height = Math.max(this.minCellHeight, parseInt(this.newRowHeight) || this.minCellHeight)

      if (this.currentRowIndex >= 0) {
        const table = this.$refs.editableTable
        const targetRow = table.rows[this.currentRowIndex]

        // 获取该行的所有单元格
        const cells = Array.from(targetRow.cells)

        // 处理合并单元格的情况
        cells.forEach(cell => {
          const rowspan = parseInt(cell.getAttribute('rowspan') || '1')

          if (rowspan > 1) {
            // 如果是合并单元格，需要调整所有被合并的行
            for (let i = 0; i < rowspan; i++) {
              const rowIndex = this.currentRowIndex + i
              if (rowIndex < table.rows.length) {
                this.setRowHeight(table.rows[rowIndex], height / rowspan)
              }
            }
          } else {
            // 普通单元格，直接设置高度
            cell.style.height = `${height}px`
            cell.style.minHeight = `${height}px`
          }
        })

        // 同时处理其他行中跨越到当前行的合并单元格
        this.adjustCrossRowMergedCells(height)
      }

      this.closeRowHeightDialog()
      this.updateTableInfo()
    },

    // 设置行高度
    setRowHeight(row, height) {
      const cells = Array.from(row.cells)
      cells.forEach(cell => {
        cell.style.height = `${height}px`
        cell.style.minHeight = `${height}px`
      })
    },

    // 调整跨行合并单元格
    adjustCrossRowMergedCells(newHeight) {
      const table = this.$refs.editableTable
      const rows = Array.from(table.rows)

      // 检查其他行中是否有跨越到当前行的合并单元格
      for (let i = 0; i < this.currentRowIndex; i++) {
        const row = rows[i]
        const cells = Array.from(row.cells)

        cells.forEach(cell => {
          const rowspan = parseInt(cell.getAttribute('rowspan') || '1')
          if (rowspan > 1 && i + rowspan > this.currentRowIndex) {
            // 这个单元格跨越到了当前行，需要重新计算高度
            const totalHeight = newHeight * (rowspan - (this.currentRowIndex - i))
            cell.style.height = `${totalHeight}px`
            cell.style.minHeight = `${totalHeight}px`
          }
        })
      }
    },

    // 应用列宽调整
    applyColumnWidth() {
      const newWidth = Math.max(this.minCellWidth, parseInt(this.newColumnWidth) || this.minCellWidth)

      if (this.currentColumnIndex >= 0) {
        const table = this.$refs.editableTable
        const tableContainer = this.$refs.tableContainer
        const rows = Array.from(table.rows)

        // 计算当前列的原始宽度
        let originalWidth = 0
        const firstRowCell = this.getCellAtColumn(rows[0], this.currentColumnIndex)
        if (firstRowCell) {
          originalWidth = firstRowCell.getBoundingClientRect().width
        }

        // 计算宽度变化量
        const widthDelta = newWidth - originalWidth

        console.log('列宽调整信息:', {
          currentColumnIndex: this.currentColumnIndex,
          originalWidth: originalWidth,
          newWidth: newWidth,
          widthDelta: widthDelta
        })

        // 遍历所有行，调整对应列的单元格宽度
        rows.forEach((row, rowIndex) => {
          const cell = this.getCellAtColumn(row, this.currentColumnIndex)
          if (cell) {
            const colspan = parseInt(cell.getAttribute('colspan') || '1')

            if (colspan > 1) {
              // 如果是合并单元格，需要考虑跨列的情况
              // 获取当前合并单元格的宽度
              const currentCellWidth = cell.getBoundingClientRect().width
              // 计算新的合并单元格宽度（只调整目标列的部分）
              const newCellWidth = currentCellWidth + widthDelta
              cell.style.width = `${newCellWidth}px`
              cell.style.minWidth = `${newCellWidth}px`
            } else {
              // 普通单元格，直接设置新宽度
              cell.style.width = `${newWidth}px`
              cell.style.minWidth = `${newWidth}px`
            }
          }
        })

        // 调整表格整体宽度
        const currentTableWidth = table.getBoundingClientRect().width
        const newTableWidth = currentTableWidth + widthDelta

        console.log('表格宽度调整:', {
          currentTableWidth: currentTableWidth,
          newTableWidth: newTableWidth,
          widthDelta: widthDelta
        })

        // 设置新的表格宽度
        table.style.width = `${newTableWidth}px`

        // 同时调整表格容器宽度
        if (tableContainer) {
          tableContainer.style.width = `${newTableWidth}px`
        }

        // 更新表格宽度输入框的值
        this.tableWidthInput = `${newTableWidth}px`
      }

      this.closeColumnWidthDialog()
      this.updateTableInfo()
    },

    // 获取指定列的单元格
    getCellAtColumn(row, targetColumnIndex) {
      const cells = Array.from(row.cells)
      let currentColumnIndex = 0

      for (let i = 0; i < cells.length; i++) {
        const cell = cells[i]
        const colspan = parseInt(cell.getAttribute('colspan') || '1')

        // 检查目标列是否在当前单元格的范围内
        if (currentColumnIndex <= targetColumnIndex && currentColumnIndex + colspan > targetColumnIndex) {
          console.log('找到目标列的单元格:', {
            cellIndex: i,
            currentColumnIndex: currentColumnIndex,
            targetColumnIndex: targetColumnIndex,
            colspan: colspan,
            cellText: cell.textContent.trim()
          })
          return cell
        }

        currentColumnIndex += colspan
      }

      console.log('未找到目标列的单元格:', {
        targetColumnIndex: targetColumnIndex,
        rowCells: cells.length
      })
      return null
    },

    // 重置表格
    resetTable() {
      if (!this.initialTableState) return

      // 重置表格尺寸
      this.tableWidthInput = this.initialTableState.width
      this.tableHeightInput = this.initialTableState.height

      // 重置单元格样式
      const table = this.$refs.editableTable
      if (table) {
        const cells = table.querySelectorAll('td')
        cells.forEach((cell, index) => {
          if (this.initialTableState.cellStyles[index]) {
            const style = this.initialTableState.cellStyles[index]
            cell.style.width = style.minWidth || ''
            cell.style.height = style.minHeight || ''
            cell.style.minWidth = style.minWidth || ''
            cell.style.minHeight = style.minHeight || ''
          }
        })
      }

      this.updateTableInfo()
    },

    // 下载表格
    downloadTable() {
      const table = this.$refs.editableTable
      if (!table) return

      // 创建表格副本
      const clonedTable = table.cloneNode(true)

      // 生成HTML
      const html = this.generateDownloadHTML(clonedTable)

      // 创建下载链接
      const blob = new Blob([html], { type: 'text/html;charset=utf-8' })
      const link = document.createElement('a')
      link.href = URL.createObjectURL(blob)
      link.download = '表格重构版本.html'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(link.href)
    },

    // 导出到Word
    async exportToWord() {
      // 检查是否有数据
      if (!this.dataRows || this.dataRows.length === 0) {
        this.$message.warning('表格中没有数据，请先添加一些内容')
        return
      }

      // 显示加载提示
      const loading = this.$loading({
        lock: true,
        text: '正在生成Word文档，请稍候...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })

      try {
        console.log('开始导出Word文档...')

        // 准备导出数据（异步处理数学公式）
        const exportData = await this.prepareExportData()
        console.log('导出数据:', exportData)

        // 动态导入API
        const { exportTableToWord } = await import('@/api/word/export')

        // 调用导出API
        const response = await exportTableToWord(exportData)

        console.log('API响应:', response)
        console.log('响应状态:', response.status)
        console.log('响应头:', response.headers)
        console.log('响应数据类型:', typeof response.data)
        console.log('响应数据大小:', response.data ? (response.data.size || response.data.byteLength || response.data.length) : 'unknown')

        // 检查响应
        if (!response || !response.data) {
          throw new Error('服务器返回数据为空')
        }

        // 检查响应状态
        if (response.status !== 200) {
          throw new Error(`服务器响应错误: ${response.status}`)
        }

        // 检查数据大小
        const dataSize = response.data.size || response.data.byteLength || response.data.length
        if (!dataSize || dataSize === 0) {
          throw new Error('返回的文件数据为空')
        }

        // 处理文件下载
        this.downloadWordFile(response, exportData.title)

        // 关闭加载提示
        loading.close()
        this.$message.success('Word文档导出成功！')

      } catch (error) {
        // 关闭加载提示
        loading.close()

        console.error('导出Word文档失败:', error)

        // 根据错误类型显示不同的提示
        let errorMessage = '导出失败'
        if (error.response) {
          // 服务器响应错误
          if (error.response.status === 404) {
            errorMessage = '导出服务不可用，请检查后端服务'
          } else if (error.response.status === 500) {
            errorMessage = '服务器内部错误，请稍后重试'
          } else {
            errorMessage = `服务器错误: ${error.response.status}`
          }
        } else if (error.code === 'NETWORK_ERROR') {
          errorMessage = '网络连接失败，请检查网络'
        } else if (error.message) {
          errorMessage = error.message
        }

        this.$message.error(errorMessage)
      }
    },

    // 测试Word导出 - 使用GET接口
    testWordExport() {
      try {
        console.log('开始测试Word导出...')

        // 直接打开GET接口URL进行下载测试
        const testUrl = '/word/wordExport/testExport'
        const baseUrl = process.env.VUE_APP_BASE_API || 'http://localhost:8080'
        const fullUrl = baseUrl + testUrl

        console.log('测试URL:', fullUrl)

        // 方法1: 直接在新窗口打开
        window.open(fullUrl, '_blank')

        this.$message.info('已在新窗口打开测试下载链接')

        // 方法2: 也可以尝试直接跳转
        // window.location.href = fullUrl

      } catch (error) {
        console.error('测试导出失败:', error)
        this.$message.error('测试导出失败: ' + error.message)
      }
    },

    // 测试POST导出 - 直接使用axios
    async testPostExport() {
      try {
        console.log('开始测试POST导出...')

        // 使用与后端测试接口相同的数据结构
        const exportData = {
          title: '测试检验记录表',
          tableWidth: 1200,
          tableHeight: 600,
          pageOrientation: 'LANDSCAPE',
          tableData: {
            headers: [
              [
                { content: '检查工序名称', rowspan: 2, colspan: 1, width: 120, height: 50 },
                { content: '检查项目及技术条件', rowspan: 2, colspan: 1, width: 200, height: 50 },
                { content: '实际检查结果', rowspan: 2, colspan: 1, width: 150, height: 50 },
                { content: '完工', rowspan: 1, colspan: 2, width: 120, height: 25 },
                { content: '', rowspan: 1, colspan: 1, width: 0, height: 25 },
                { content: '操作员', rowspan: 2, colspan: 1, width: 100, height: 50 },
                { content: '班组长', rowspan: 2, colspan: 1, width: 100, height: 50 },
                { content: '检验员', rowspan: 2, colspan: 1, width: 100, height: 50 }
              ],
              [
                { content: '', rowspan: 1, colspan: 1, width: 0, height: 25 },
                { content: '', rowspan: 1, colspan: 1, width: 0, height: 25 },
                { content: '', rowspan: 1, colspan: 1, width: 0, height: 25 },
                { content: '月', rowspan: 1, colspan: 1, width: 60, height: 25 },
                { content: '日', rowspan: 1, colspan: 1, width: 60, height: 25 },
                { content: '', rowspan: 1, colspan: 1, width: 0, height: 25 },
                { content: '', rowspan: 1, colspan: 1, width: 0, height: 25 },
                { content: '', rowspan: 1, colspan: 1, width: 0, height: 25 }
              ]
            ],
            dataRows: [
              [
                { content: '外观检查', hasMath: false, width: 120, height: 50 },
                { content: '表面无划痕、无变形', hasMath: false, width: 200, height: 50 },
                { content: '合格', hasMath: false, width: 150, height: 50 },
                { content: '12', hasMath: false, width: 60, height: 50 },
                { content: '25', hasMath: false, width: 60, height: 50 },
                { content: '张三', hasMath: false, width: 100, height: 50 },
                { content: '李四', hasMath: false, width: 100, height: 50 },
                { content: '王五', hasMath: false, width: 100, height: 50 }
              ]
            ]
          }
        }

        console.log('测试数据:', exportData)

        // 直接使用axios发送请求
        const axios = window.axios || this.$http
        const response = await axios({
          url: '/word/wordExport/exportTable',
          method: 'post',
          data: exportData,
          responseType: 'blob',
          timeout: 60000
        })

        console.log('直接axios响应:', response)

        if (response && response.data) {
          this.downloadWordFile(response, '测试POST导出')
          this.$message.success('测试POST导出成功！')
        } else {
          throw new Error('响应数据为空')
        }

      } catch (error) {
        console.error('测试POST导出失败:', error)
        this.$message.error('测试POST导出失败: ' + error.message)
      }
    },

    // 准备导出数据
    async prepareExportData() {
      const table = this.$refs.editableTable
      if (!table) {
        throw new Error('未找到表格元素')
      }

      // 获取表格尺寸
      const tableRect = table.getBoundingClientRect()

      // 准备表头数据 - 简化处理，直接使用固定结构
      const headers = [
        [
          { content: '检查工序名称', rowspan: 2, colspan: 1, width: 120, height: 50 },
          { content: '检查项目及技术条件', rowspan: 2, colspan: 1, width: 200, height: 50 },
          { content: '实际检查结果', rowspan: 2, colspan: 1, width: 150, height: 50 },
          { content: '完工', rowspan: 1, colspan: 2, width: 120, height: 25 },
          { content: '', rowspan: 1, colspan: 1, width: 0, height: 25 }, // 占位符
          { content: '操作员', rowspan: 2, colspan: 1, width: 100, height: 50 },
          { content: '班组长', rowspan: 2, colspan: 1, width: 100, height: 50 },
          { content: '检验员', rowspan: 2, colspan: 1, width: 100, height: 50 }
        ],
        [
          { content: '', rowspan: 1, colspan: 1, width: 0, height: 25 }, // 占位符
          { content: '', rowspan: 1, colspan: 1, width: 0, height: 25 }, // 占位符
          { content: '', rowspan: 1, colspan: 1, width: 0, height: 25 }, // 占位符
          { content: '月', rowspan: 1, colspan: 1, width: 60, height: 25 },
          { content: '日', rowspan: 1, colspan: 1, width: 60, height: 25 },
          { content: '', rowspan: 1, colspan: 1, width: 0, height: 25 }, // 占位符
          { content: '', rowspan: 1, colspan: 1, width: 0, height: 25 }, // 占位符
          { content: '', rowspan: 1, colspan: 1, width: 0, height: 25 }  // 占位符
        ]
      ]

      // 准备数据行 - 确保每行都有8个单元格
      const dataRows = this.dataRows.map(row => {
        const processedRow = []
        for (let i = 0; i < 8; i++) {
          const cell = row[i] || { content: '', hasMath: false }
          processedRow.push({
            content: cell.content || '',
            hasMath: Boolean(cell.hasMath),
            mathML: cell.mathML || null, // 添加mathML字段
            width: this.getColumnWidth(i),
            height: 50
          })
        }
        return processedRow
      })

      // 过滤掉空行
      const filteredDataRows = dataRows.filter(row =>
        row.some(cell => cell.content && cell.content.trim() !== '')
      )

      // 处理数学公式转换
      console.log('开始处理数学公式转换...')
      console.log('转换前的数据行:', filteredDataRows)

      const processedTableData = await this.processMathFormulas({
        headers: headers,
        dataRows: filteredDataRows
      })

      console.log('转换后的数据行:', processedTableData.dataRows)

      // 检查是否有MathML数据
      let mathMLCount = 0
      processedTableData.dataRows.forEach((row, rowIndex) => {
        row.forEach((cell, cellIndex) => {
          if (cell.mathML) {
            mathMLCount++
            console.log(`发现MathML数据 [${rowIndex}, ${cellIndex}]:`, {
              content: cell.content,
              hasMath: cell.hasMath,
              mathML: cell.mathML.substring(0, 100) + '...'
            })
          }
        })
      })
      console.log(`总共找到 ${mathMLCount} 个MathML数据`)

      const exportData = {
        title: '检验记录表',
        tableWidth: Math.round(tableRect.width) || 1200,
        tableHeight: Math.round(tableRect.height) || 600,
        pageOrientation: 'LANDSCAPE', // 默认横向纸张
        tableData: processedTableData
      }

      console.log('准备的导出数据:', {
        title: exportData.title,
        tableSize: `${exportData.tableWidth}x${exportData.tableHeight}`,
        headerRows: exportData.tableData.headers.length,
        dataRows: exportData.tableData.dataRows.length,
        totalCells: exportData.tableData.dataRows.reduce((sum, row) => sum + row.length, 0)
      })

      // 详细打印表头数据
      console.log('表头详细数据:')
      exportData.tableData.headers.forEach((row, rowIndex) => {
        console.log(`第${rowIndex + 1}行表头:`)
        row.forEach((cell, colIndex) => {
          if (cell.content && cell.content.trim() !== '') {
            console.log(`  列${colIndex}: "${cell.content}", rowspan=${cell.rowspan}, colspan=${cell.colspan}`)
          }
        })
      })

      return exportData
    },

    // 处理数学公式转换
    async processMathFormulas(tableData) {
      try {
        // 动态导入数学公式工具
        const mathFormulaUtils = await import('@/utils/math-formula-utils.js')
        const mathUtils = mathFormulaUtils.default

        console.log('开始处理表格中的数学公式...')

        // 处理表格数据中的数学公式
        const processedTableData = await mathUtils.processTableData(tableData)

        console.log('数学公式处理完成')
        return processedTableData

      } catch (error) {
        console.error('数学公式处理失败:', error)
        // 如果数学公式处理失败，返回原始数据
        console.warn('使用原始数据继续导出')
        return tableData
      }
    },

    // 测试数学公式转换
    async testMathFormulas() {
      try {
        console.log('开始测试数学公式转换...')

        // 动态导入数学公式工具
        const mathFormulaUtils = await import('@/utils/math-formula-utils.js')
        const mathUtils = mathFormulaUtils.default

        // 测试用例
        const testCases = [
          '$x^2 + y^2 = z^2$',
          '$\\frac{a}{b} = \\frac{c}{d}$',
          '$\\sqrt{x + y}$',
          '$\\alpha + \\beta = \\gamma$'
        ]

        console.log('测试用例:', testCases)

        for (const latex of testCases) {
          console.log(`\n测试公式: ${latex}`)

          // 检测是否包含数学公式
          const hasMath = mathUtils.containsMath(latex)
          console.log(`包含数学公式: ${hasMath}`)

          if (hasMath) {
            // 转换为MathML
            const mathML = await mathUtils.latexToMathML(latex)
            console.log(`MathML结果: ${mathML}`)

            // 验证MathML格式
            const isValid = mathUtils.isValidMathML(mathML)
            console.log(`MathML有效: ${isValid}`)
          }
        }

        // 测试表格数据处理
        const testTableData = {
          headers: [],
          dataRows: [
            [
              { content: '测试公式1', hasMath: false },
              { content: '$x^2 + y^2 = z^2$', hasMath: false },
              { content: '勾股定理', hasMath: false }
            ],
            [
              { content: '测试公式2', hasMath: false },
              { content: '$\\frac{a}{b} = \\frac{c}{d}$', hasMath: false },
              { content: '分数相等', hasMath: false }
            ]
          ]
        }

        console.log('\n测试表格数据处理...')
        console.log('原始数据:', testTableData)

        const processedData = await mathUtils.processTableData(testTableData)
        console.log('处理后数据:', processedData)

        // 检查结果
        let mathMLCount = 0
        processedData.dataRows.forEach((row, rowIndex) => {
          row.forEach((cell, cellIndex) => {
            if (cell.mathML) {
              mathMLCount++
              console.log(`找到MathML [${rowIndex}, ${cellIndex}]:`, {
                content: cell.content,
                hasMath: cell.hasMath,
                mathML: cell.mathML.substring(0, 50) + '...'
              })
            }
          })
        })

        this.$message.success(`数学公式测试完成！找到 ${mathMLCount} 个MathML转换结果`)

      } catch (error) {
        console.error('数学公式测试失败:', error)
        this.$message.error('数学公式测试失败: ' + error.message)
      }
    },

    // 获取列宽度
    getColumnWidth(columnIndex) {
      const widths = [120, 200, 150, 60, 60, 100, 100, 100]
      return widths[columnIndex] || 120
    },

    // 下载Word文件
    downloadWordFile(response, title) {
      try {
        console.log('开始处理文件下载...')
        console.log('响应对象:', response)
        console.log('响应数据类型:', typeof response.data)
        console.log('响应数据大小:', response.data ? response.data.size || response.data.byteLength || response.data.length : 'unknown')

        // 检查响应数据
        if (!response || !response.data) {
          throw new Error('响应数据为空')
        }

        // 创建Blob对象
        const blob = new Blob([response.data], {
          type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        })

        console.log('Blob创建成功，大小:', blob.size)

        // 检查Blob大小
        if (blob.size === 0) {
          throw new Error('生成的文件为空')
        }

        // 创建下载链接
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url

        // 生成文件名
        const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '')
        const fileName = `${title || '表格导出'}_${timestamp}.docx`
        link.download = fileName

        console.log('准备下载文件:', fileName)

        // 设置链接属性以确保下载
        link.style.display = 'none'
        link.target = '_blank'

        // 触发下载
        document.body.appendChild(link)

        // 延迟点击以确保链接已添加到DOM
        setTimeout(() => {
          link.click()
          console.log('下载链接已点击')

          // 延迟清理以确保下载开始
          setTimeout(() => {
            document.body.removeChild(link)
            window.URL.revokeObjectURL(url)
            console.log('Word文件下载完成:', fileName)
          }, 100)
        }, 10)

      } catch (error) {
        console.error('下载Word文件失败:', error)
        throw new Error('下载文件失败: ' + error.message)
      }
    },

    // 生成下载HTML
    generateDownloadHTML(table) {
      const currentDate = new Date().toLocaleString('zh-CN')

      return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>表格重构版本</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        table { border-collapse: collapse; width: 100%; }
        td { border: 1px solid #ddd; padding: 8px; text-align: center; }
        .header { text-align: center; margin-bottom: 20px; }
    </style>
</head>
<body>
    <div class="header">
        <h2>表格重构版本</h2>
        <p>生成时间: ${currentDate}</p>
    </div>
    ${table.outerHTML}
</body>
</html>`
    }
  }
}
</script>

<style scoped>
.table-editor-page {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: #f8f9fa;
  min-height: 100vh;
  padding: 10px;
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
}

/* 控制面板样式 */
.control-panel {
  background: white;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #e9ecef;
  /* 进一步压缩控制面板高度 */
  max-height: 25vh;
  overflow-y: auto;
}

.control-panel h3 {
  margin: 0 0 8px 0;
  color: #333;
  font-weight: 600;
  font-size: 15px;
}

.control-section {
  margin-bottom: 8px;
  padding-bottom: 6px;
  border-bottom: 1px solid #f1f3f5;
}

.control-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.control-section h4 {
  margin: 0 0 6px 0;
  color: #495057;
  font-weight: 500;
  font-size: 12px;
}

.control-row {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

/* 左右分栏布局 */
.control-row-split {
  display: flex;
  gap: 20px;
  align-items: flex-start;
}

.control-column {
  flex: 1;
  min-width: 0;
}

/* 紧凑型控制行 */
.control-row-compact {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.control-item {
  flex: 1;
  min-width: 200px;
}

/* 紧凑型控制项 */
.control-item-compact {
  flex: 1;
  min-width: 120px;
}

.control-item label {
  display: block;
  margin-bottom: 6px;
  color: #495057;
  font-weight: 500;
  font-size: 13px;
}

.control-item input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ced4da;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.control-item input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.control-item select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ced4da;
  border-radius: 6px;
  font-size: 14px;
  background-color: white;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
  cursor: pointer;
}

.control-item select:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

/* 美化的尺寸输入框样式 */
.input-with-icon {
  position: relative;
  display: flex;
  align-items: center;
}

.dimension-input {
  width: 100%;
  padding: 10px 35px 10px 12px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
}

.dimension-input:focus {
  outline: none;
  border-color: #007bff;
  background: linear-gradient(135deg, #ffffff 0%, #f0f8ff 100%);
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1), 0 4px 12px rgba(0, 123, 255, 0.15);
  transform: translateY(-1px);
}

.dimension-input:hover {
  border-color: #6c757d;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);
}

.width-input:focus {
  border-color: #28a745;
  background: linear-gradient(135deg, #ffffff 0%, #f0fff4 100%);
  box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.1), 0 4px 12px rgba(40, 167, 69, 0.15);
}

.height-input:focus {
  border-color: #ffc107;
  background: linear-gradient(135deg, #ffffff 0%, #fffbf0 100%);
  box-shadow: 0 0 0 3px rgba(255, 193, 7, 0.1), 0 4px 12px rgba(255, 193, 7, 0.15);
}

.input-icon {
  position: absolute;
  right: 10px;
  font-size: 16px;
  pointer-events: none;
  opacity: 0.6;
  transition: all 0.3s ease;
}

.dimension-input:focus + .input-icon {
  opacity: 1;
  transform: scale(1.1);
}

.width-input:focus + .input-icon {
  filter: hue-rotate(120deg);
}

.height-input:focus + .input-icon {
  filter: hue-rotate(45deg);
}

/* 统一尺寸输入框样式 */
.uniform-width-input:focus {
  border-color: #17a2b8;
  background: linear-gradient(135deg, #ffffff 0%, #f0fcff 100%);
  box-shadow: 0 0 0 3px rgba(23, 162, 184, 0.1), 0 4px 12px rgba(23, 162, 184, 0.15);
}

.uniform-height-input:focus {
  border-color: #6f42c1;
  background: linear-gradient(135deg, #ffffff 0%, #f8f0ff 100%);
  box-shadow: 0 0 0 3px rgba(111, 66, 193, 0.1), 0 4px 12px rgba(111, 66, 193, 0.15);
}

.input-unit {
  position: absolute;
  right: 10px;
  font-size: 12px;
  font-weight: 600;
  color: #6c757d;
  pointer-events: none;
  background: rgba(255, 255, 255, 0.9);
  padding: 2px 4px;
  border-radius: 3px;
  transition: all 0.3s ease;
}

.dimension-input:focus + .input-unit {
  color: #495057;
  background: rgba(255, 255, 255, 1);
  transform: scale(1.05);
}

.uniform-width-input:focus + .input-unit {
  color: #17a2b8;
}

.uniform-height-input:focus + .input-unit {
  color: #6f42c1;
}

/* 输入框动画效果 */
@keyframes inputFocus {
  0% {
    transform: translateY(0) scale(1);
  }
  50% {
    transform: translateY(-2px) scale(1.02);
  }
  100% {
    transform: translateY(-1px) scale(1.01);
  }
}

.dimension-input:focus {
  animation: inputFocus 0.3s ease;
}

/* 输入框验证状态 */
.dimension-input:valid {
  border-color: #28a745;
}

.dimension-input:invalid {
  border-color: #dc3545;
}

/* 输入框禁用状态 */
.dimension-input:disabled {
  background: #f8f9fa;
  color: #6c757d;
  cursor: not-allowed;
  opacity: 0.6;
}

/* 输入框占位符样式 */
.dimension-input::placeholder {
  color: #adb5bd;
  font-style: italic;
  transition: color 0.3s ease;
}

.dimension-input:focus::placeholder {
  color: #6c757d;
}

/* 响应式输入框样式 */
@media (max-width: 768px) {
  .dimension-input {
    padding: 8px 30px 8px 10px;
    font-size: 13px;
  }

  .input-icon {
    font-size: 14px;
    right: 8px;
  }

  .input-unit {
    font-size: 11px;
    right: 8px;
  }
}

@media (max-width: 480px) {
  .dimension-input {
    padding: 6px 25px 6px 8px;
    font-size: 12px;
  }

  .input-icon {
    font-size: 12px;
    right: 6px;
  }

  .input-unit {
    font-size: 10px;
    right: 6px;
  }
}

.info-display {
  background: #f8f9fa;
  padding: 12px;
  border-radius: 6px;
  font-size: 13px;
  color: #495057;
}

.info-display div {
  margin-bottom: 4px;
}

.info-display div:last-child {
  margin-bottom: 0;
}

/* 紧凑型信息显示 */
.info-display-compact {
  background: #f8f9fa;
  padding: 8px 10px;
  border-radius: 4px;
  font-size: 12px;
  color: #495057;
  line-height: 1.4;
}

.info-display-compact div {
  margin-bottom: 2px;
}

.info-display-compact div:last-child {
  margin-bottom: 0;
}

.control-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.reset-button, .download-button {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.reset-button {
  background: #6c757d;
  color: white;
}

.reset-button:hover {
  background: #545b62;
  transform: translateY(-1px);
}

.download-button {
  background: linear-gradient(135deg, #007bff, #0056b3);
  color: white;
  box-shadow: 0 2px 5px rgba(0, 123, 255, 0.3);
}

.download-button:hover {
  background: linear-gradient(135deg, #0056b3, #004085);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 123, 255, 0.4);
}

.export-word-button {
  background: linear-gradient(135deg, #28a745, #1e7e34);
  color: white;
  box-shadow: 0 2px 5px rgba(40, 167, 69, 0.3);
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.export-word-button:hover {
  background: linear-gradient(135deg, #1e7e34, #155724);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(40, 167, 69, 0.4);
}

.export-word-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
}

.test-export-button {
  background: linear-gradient(135deg, #17a2b8, #138496);
  color: white;
  box-shadow: 0 2px 5px rgba(23, 162, 184, 0.3);
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.test-export-button:hover {
  background: linear-gradient(135deg, #138496, #117a8b);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(23, 162, 184, 0.4);
}

.test-export-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(23, 162, 184, 0.3);
}

.test-post-button {
  background: linear-gradient(135deg, #6f42c1, #5a32a3);
  color: white;
  box-shadow: 0 2px 5px rgba(111, 66, 193, 0.3);
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.test-post-button:hover {
  background: linear-gradient(135deg, #5a32a3, #4c2a85);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(111, 66, 193, 0.4);
}

.test-post-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(111, 66, 193, 0.3);
}

.test-math-button {
  background: linear-gradient(135deg, #e83e8c, #d91a72);
  color: white;
  box-shadow: 0 2px 5px rgba(232, 62, 140, 0.3);
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.test-math-button:hover {
  background: linear-gradient(135deg, #d91a72, #c51162);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(232, 62, 140, 0.4);
}

.test-math-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(232, 62, 140, 0.3);
}

.test-button {
  background: #28a745;
  color: white;
  box-shadow: 0 2px 5px rgba(40, 167, 69, 0.3);
}

.test-button:hover {
  background: #218838;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(40, 167, 69, 0.4);
}

/* 统一尺寸按钮样式 */
.uniform-size-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background: linear-gradient(135deg, #6f42c1, #5a32a3);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(111, 66, 193, 0.3);
  min-width: 160px;
  justify-content: center;
}

.uniform-size-button:hover {
  background: linear-gradient(135deg, #5a32a3, #4c2a85);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(111, 66, 193, 0.4);
}

.uniform-size-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 6px rgba(111, 66, 193, 0.3);
}

.button-icon {
  font-size: 16px;
  filter: brightness(1.2);
}

/* 添加空白行按钮样式 */
.add-row-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background: linear-gradient(135deg, #28a745, #20a83a);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
  min-width: 140px;
  justify-content: center;
}

.add-row-button:hover {
  background: linear-gradient(135deg, #20a83a, #1e7e34);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.4);
}

.add-row-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 6px rgba(40, 167, 69, 0.3);
}

.add-row-button .button-icon {
  font-size: 14px;
  filter: brightness(1.3);
}

/* 数学公式帮助按钮样式 */
.math-help-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background: linear-gradient(135deg, #17a2b8, #138496);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(23, 162, 184, 0.3);
  min-width: 150px;
  justify-content: center;
}

.math-help-button:hover {
  background: linear-gradient(135deg, #138496, #117a8b);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(23, 162, 184, 0.4);
}

.math-help-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 6px rgba(23, 162, 184, 0.3);
}

.math-help-button .button-icon {
  font-size: 16px;
  font-weight: bold;
}

/* 表格容器样式 */
.table-wrapper {
  background: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #e9ecef;
  position: relative;
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
}

.table-container {
  border: 2px solid #dee2e6;
  border-radius: 8px;
  position: relative;
  transition: all 0.3s ease;
  /* 占用剩余空间而不是固定高度 */
  flex: 1;
  min-height: 0;
  overflow: hidden; /* 防止内容溢出 */
  /* 使用flex布局确保滚动容器正确工作 */
  display: flex;
  flex-direction: column;
}

.table-scroll-container {
  overflow: auto;
  flex: 1;
  min-height: 0;
  position: relative;
  /* 自定义滚动条样式 */
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 #f1f1f1;
}

table {
  border-collapse: collapse;
  table-layout: fixed;
  background: white;
  width: 100%;
  min-width: 100%;
}

/* 表头单元格样式 */
.header-cell {
  border: 1px solid #dee2e6;
  padding: 12px 15px;
  text-align: center;
  vertical-align: middle;
  font-size: 14px;
  position: relative;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  font-weight: 600;
  color: #495057;
  cursor: default;
  user-select: none;
  min-width: 20px;
  min-height: 30px;
  /* 内容溢出处理 */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  transition: background 0.2s ease;
}

.header-cell:hover {
  background: linear-gradient(135deg, #e9ecef, #dee2e6);
}

/* 可编辑单元格样式 */
.editable-cell {
  border: 1px solid #dee2e6;
  padding: 12px 15px;
  text-align: center;
  vertical-align: middle;
  font-size: 14px;
  position: relative;
  background: white;
  cursor: pointer;
  color: #495057;
  min-width: 20px;
  min-height: 30px;
  /* 内容溢出处理 */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  transition: all 0.2s ease;
}

.editable-cell:hover {
  background-color: #f8f9fa;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.1);
}

/* 编辑状态的单元格 */
.editable-cell[contenteditable="true"] {
  background: #fff3cd;
  border-color: #ffc107;
  box-shadow: 0 0 0 3px rgba(255, 193, 7, 0.2);
  outline: none;
  cursor: text;
  /* 编辑时允许换行但保持在单元格内 */
  white-space: pre-wrap;
  overflow: hidden;
}

.editable-cell[contenteditable="true"]:focus {
  background: #fff3cd;
  border-color: #ffc107;
}

/* 包含数学公式的单元格样式 */
.editable-cell.has-math {
  background: linear-gradient(135deg, #f0f8ff, #e6f3ff);
  border-left: 3px solid #007bff;
}

.editable-cell.has-math:hover {
  background: linear-gradient(135deg, #e6f3ff, #cce7ff);
  border-color: #0056b3;
}

/* MathJax公式样式优化 */
.editable-cell .MathJax {
  font-size: 1em !important;
}

.editable-cell .MathJax_Display {
  margin: 0.2em 0 !important;
}

/* 编辑状态下的数学公式单元格 */
.editable-cell.has-math[contenteditable="true"] {
  background: #fff8e1;
  border-color: #ff9800;
  font-family: 'Courier New', monospace;
}

.editable-cell.has-math[contenteditable="true"]:focus {
  background: #fff8e1;
  border-color: #ff9800;
  box-shadow: 0 0 0 3px rgba(255, 152, 0, 0.2);
}

/* 右键菜单样式 */
.context-menu {
  position: fixed;
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  padding: 8px 0;
  z-index: 1000;
  min-width: 160px;
  font-size: 14px;
}

.context-menu-item {
  padding: 10px 16px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  color: #333;
}

.context-menu-item:hover {
  background-color: #f8f9fa;
  color: #007bff;
}

/* 右键菜单分割线 */
.context-menu-divider {
  height: 1px;
  background: #e9ecef;
  margin: 4px 0;
}

/* 删除菜单项样式 */
.context-menu-item.delete-item {
  color: #dc3545;
}

.context-menu-item.delete-item:hover {
  background: #f8d7da;
  color: #721c24;
}

/* 对话框样式 */
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1001;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dialog {
  background: white;
  border-radius: 12px;
  padding: 24px;
  min-width: 320px;
  max-width: 500px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
  animation: dialogFadeIn 0.3s ease;
}

@keyframes dialogFadeIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.dialog h3 {
  margin: 0 0 20px 0;
  color: #333;
  font-weight: 600;
  font-size: 18px;
}

.dialog-content {
  margin-bottom: 20px;
}

.dialog-content label {
  display: block;
  margin-bottom: 8px;
  color: #495057;
  font-weight: 500;
  font-size: 14px;
}

.dialog-content input {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #ced4da;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.dialog-content input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.dialog-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.btn-cancel, .btn-confirm {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-cancel {
  background: #6c757d;
  color: white;
}

.btn-cancel:hover {
  background: #545b62;
}

.btn-confirm {
  background: #007bff;
  color: white;
}

.btn-confirm:hover {
  background: #0056b3;
}

/* 统一尺寸对话框样式 */
.uniform-size-dialog {
  min-width: 480px;
  max-width: 600px;
}

.uniform-size-section {
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e9ecef;
}

.uniform-size-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.uniform-size-section h4 {
  margin: 0 0 12px 0;
  color: #495057;
  font-weight: 600;
  font-size: 14px;
}

.current-settings {
  display: flex;
  gap: 20px;
  background: #f8f9fa;
  padding: 12px;
  border-radius: 6px;
}

.setting-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.setting-label {
  font-size: 12px;
  color: #6c757d;
  font-weight: 500;
}

.setting-value {
  font-size: 16px;
  color: #495057;
  font-weight: 600;
}

.uniform-inputs {
  display: flex;
  gap: 16px;
}

.uniform-input-group {
  flex: 1;
}

.uniform-input-group label {
  display: block;
  margin-bottom: 6px;
  color: #495057;
  font-weight: 500;
  font-size: 13px;
}

.uniform-dialog-input {
  padding: 8px 30px 8px 10px;
  font-size: 13px;
}

.quick-presets {
  text-align: center;
}

.preset-buttons {
  display: flex;
  gap: 12px;
  justify-content: center;
}

.preset-btn {
  padding: 8px 16px;
  border: 2px solid transparent;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #f8f9fa;
  color: #495057;
}

.preset-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.small-preset:hover {
  border-color: #28a745;
  background: #d4edda;
  color: #155724;
}

.medium-preset:hover {
  border-color: #007bff;
  background: #d1ecf1;
  color: #0c5460;
}

.large-preset:hover {
  border-color: #ffc107;
  background: #fff3cd;
  color: #856404;
}

/* 数学公式帮助对话框样式 */
.math-help-dialog {
  min-width: 600px;
  max-width: 800px;
  max-height: 80vh;
  overflow-y: auto;
}

.math-help-section {
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e9ecef;
}

.math-help-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.math-help-section h4 {
  margin: 0 0 12px 0;
  color: #495057;
  font-weight: 600;
  font-size: 14px;
}

.math-examples {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.math-example {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 3px solid #007bff;
}

.example-label {
  font-weight: 500;
  color: #495057;
  min-width: 80px;
}

.example-input {
  font-family: 'Courier New', monospace;
  background: white;
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid #dee2e6;
  font-size: 13px;
}

.symbol-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.symbol-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #dee2e6;
}

.symbol-display {
  font-size: 16px;
  font-weight: bold;
}

.symbol-latex {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  color: #6c757d;
}

.quick-examples {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.example-btn {
  padding: 6px 12px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: background 0.2s ease;
}

.example-btn:hover {
  background: #0056b3;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .table-editor-page {
    padding: 8px;
  }

  .control-panel {
    padding: 10px;
    max-height: 12vh;
  }

  .control-row, .control-row-compact {
    flex-direction: column;
  }

  .control-row-split {
    flex-direction: column;
    gap: 12px;
  }

  .control-item, .control-item-compact {
    min-width: auto;
  }

  .control-buttons {
    flex-direction: column;
  }

  .table-wrapper {
    padding: 12px;
  }
}

@media (max-width: 480px) {
  .table-editor-page {
    padding: 5px;
  }

  .control-panel {
    padding: 8px;
    max-height: 10vh;
  }

  .control-row-split {
    gap: 8px;
  }

  .table-wrapper {
    padding: 8px;
  }

  .table-scroll-container::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.control-panel, .table-wrapper {
  animation: fadeIn 0.3s ease;
}

/* 表格滚动容器的滚动条样式 */
.table-scroll-container::-webkit-scrollbar {
  width: 12px;
  height: 12px;
}

.table-scroll-container::-webkit-scrollbar-track {
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.table-scroll-container::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #007bff, #0056b3);
  border-radius: 6px;
  border: 2px solid #f8f9fa;
}

.table-scroll-container::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #0056b3, #004085);
}

.table-scroll-container::-webkit-scrollbar-corner {
  background: #f8f9fa;
}

/* 表格包装器的滚动条样式（如果需要） */
.table-wrapper::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.table-wrapper::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.table-wrapper::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.table-wrapper::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
