/**
 * 前端数学公式处理工具
 * 用于LaTeX转MathML的处理
 */

class MathFormulaUtils {
  constructor() {
    this.mathJaxReady = false;
    this.initPromise = null;
  }

  /**
   * 初始化MathJax
   */
  async initializeMathJax() {
    if (this.initPromise) {
      return this.initPromise;
    }

    this.initPromise = new Promise(async (resolve, reject) => {
      try {
        // 检查MathJax是否已加载
        if (window.MathJax && window.MathJax.tex2mml) {
          this.mathJaxReady = true;
          resolve();
          return;
        }

        console.log('开始初始化MathJax...');

        // 设置MathJax配置
        window.MathJax = {
          tex: {
            inlineMath: [['$', '$'], ['\\(', '\\)']],
            displayMath: [['$$', '$$'], ['\\[', '\\]']],
            packages: ['base', 'ams', 'newcommand', 'textmacros']
          },
          startup: {
            ready: () => {
              console.log('MathJax已准备就绪');
              this.mathJaxReady = true;
              window.MathJax.startup.defaultReady();
              resolve();
            }
          },
          loader: {
            load: ['[tex]/ams', '[tex]/newcommand', '[tex]/textmacros']
          }
        };

        // 动态加载MathJax
        const script = document.createElement('script');
        script.src = 'https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js';
        script.async = true;
        script.onload = () => {
          console.log('MathJax脚本加载完成');
        };
        script.onerror = () => {
          console.error('MathJax脚本加载失败');
          reject(new Error('MathJax脚本加载失败'));
        };
        document.head.appendChild(script);

        // 设置超时
        setTimeout(() => {
          if (!this.mathJaxReady) {
            reject(new Error('MathJax初始化超时'));
          }
        }, 10000);

      } catch (error) {
        console.error('MathJax初始化失败:', error);
        reject(error);
      }
    });

    return this.initPromise;
  }

  /**
   * 将LaTeX转换为MathML
   * @param {string} latex - LaTeX字符串
   * @returns {Promise<string>} MathML字符串
   */
  async latexToMathML(latex) {
    try {
      if (!latex || typeof latex !== 'string') {
        return '';
      }

      // 确保MathJax已准备就绪
      if (!this.mathJaxReady) {
        await this.initializeMathJax();
      }

      // 清理LaTeX字符串
      const cleanLatex = this.cleanLatexString(latex);
      
      if (!cleanLatex) {
        return '';
      }

      console.log('转换LaTeX:', cleanLatex);

      // 使用MathJax转换
      const mathML = window.MathJax.tex2mml(cleanLatex);
      
      // 清理MathML输出
      const cleanedMathML = this.cleanMathMLString(mathML);
      
      console.log('转换结果MathML:', cleanedMathML);
      
      return cleanedMathML;
      
    } catch (error) {
      console.error('LaTeX转MathML失败:', error);
      return '';
    }
  }

  /**
   * 清理LaTeX字符串
   * @param {string} latex - 原始LaTeX字符串
   * @returns {string} 清理后的LaTeX字符串
   */
  cleanLatexString(latex) {
    if (!latex) return '';
    
    // 移除外层的$符号
    let cleaned = latex.trim();
    
    // 处理行内公式 $...$
    if (cleaned.startsWith('$') && cleaned.endsWith('$') && cleaned.length > 2) {
      cleaned = cleaned.slice(1, -1);
    }
    
    // 处理显示公式 $$...$$
    if (cleaned.startsWith('$$') && cleaned.endsWith('$$') && cleaned.length > 4) {
      cleaned = cleaned.slice(2, -2);
    }
    
    // 处理LaTeX行内公式 \(...\)
    if (cleaned.startsWith('\\(') && cleaned.endsWith('\\)')) {
      cleaned = cleaned.slice(2, -2);
    }
    
    // 处理LaTeX显示公式 \[...\]
    if (cleaned.startsWith('\\[') && cleaned.endsWith('\\]')) {
      cleaned = cleaned.slice(2, -2);
    }
    
    return cleaned.trim();
  }

  /**
   * 清理MathML字符串
   * @param {string} mathML - 原始MathML字符串
   * @returns {string} 清理后的MathML字符串
   */
  cleanMathMLString(mathML) {
    if (!mathML) return '';
    
    // 移除不必要的属性和命名空间
    let cleaned = mathML
      .replace(/\s+xmlns[^=]*="[^"]*"/g, '') // 移除命名空间
      .replace(/\s+data-[^=]*="[^"]*"/g, '') // 移除data属性
      .replace(/\s+aria-[^=]*="[^"]*"/g, '') // 移除aria属性
      .replace(/\s+class="[^"]*"/g, '') // 移除class属性
      .replace(/\s+style="[^"]*"/g, '') // 移除style属性
      .replace(/\s+id="[^"]*"/g, ''); // 移除id属性
    
    // 格式化XML
    cleaned = cleaned.replace(/>\s+</g, '><');
    
    return cleaned.trim();
  }

  /**
   * 检测字符串是否包含数学公式
   * @param {string} text - 要检测的文本
   * @returns {boolean} 是否包含数学公式
   */
  containsMath(text) {
    if (!text || typeof text !== 'string') {
      return false;
    }
    
    const mathPatterns = [
      /\$[^$]+\$/,           // 行内公式 $...$
      /\$\$[^$]+\$\$/,       // 显示公式 $$...$$
      /\\\([^)]+\\\)/,       // LaTeX行内公式 \(...\)
      /\\\[[^\]]+\\\]/,      // LaTeX显示公式 \[...\]
      /\\[a-zA-Z]+/          // LaTeX命令
    ];
    
    return mathPatterns.some(pattern => pattern.test(text));
  }

  /**
   * 批量处理数据单元格中的数学公式
   * @param {Array} dataCells - 数据单元格数组
   * @returns {Promise<Array>} 处理后的数据单元格数组
   */
  async processDataCells(dataCells) {
    if (!Array.isArray(dataCells)) {
      return dataCells;
    }

    const processedCells = [];
    
    for (const cell of dataCells) {
      const processedCell = { ...cell };
      
      if (cell.content && this.containsMath(cell.content)) {
        try {
          console.log('检测到数学公式:', cell.content);
          
          // 转换LaTeX为MathML
          const mathML = await this.latexToMathML(cell.content);
          
          if (mathML) {
            processedCell.hasMath = true;
            processedCell.mathML = mathML;
            console.log('公式转换成功:', mathML);
          } else {
            console.warn('公式转换失败，保持原有标记');
            processedCell.hasMath = true; // 保持原有的数学公式标记
          }
        } catch (error) {
          console.error('处理数学公式失败:', error);
          processedCell.hasMath = false;
        }
      }
      
      processedCells.push(processedCell);
    }
    
    return processedCells;
  }

  /**
   * 处理表格数据中的所有数学公式
   * @param {Object} tableData - 表格数据对象
   * @returns {Promise<Object>} 处理后的表格数据对象
   */
  async processTableData(tableData) {
    if (!tableData || !tableData.dataRows) {
      return tableData;
    }

    console.log('开始处理表格数据中的数学公式...');
    
    const processedTableData = { ...tableData };
    const processedDataRows = [];

    for (let i = 0; i < tableData.dataRows.length; i++) {
      const row = tableData.dataRows[i];
      console.log(`处理第${i + 1}行数据...`);
      
      const processedRow = await this.processDataCells(row);
      processedDataRows.push(processedRow);
    }

    processedTableData.dataRows = processedDataRows;
    
    console.log('表格数据处理完成');
    return processedTableData;
  }

  /**
   * 验证MathML格式
   * @param {string} mathML - MathML字符串
   * @returns {boolean} 是否为有效的MathML
   */
  isValidMathML(mathML) {
    if (!mathML || typeof mathML !== 'string') {
      return false;
    }

    try {
      const parser = new DOMParser();
      const doc = parser.parseFromString(mathML, 'text/xml');
      
      // 检查是否有解析错误
      const parserError = doc.querySelector('parsererror');
      if (parserError) {
        return false;
      }
      
      // 检查根元素是否为math
      const rootElement = doc.documentElement;
      return rootElement && rootElement.tagName.toLowerCase() === 'math';
      
    } catch (error) {
      return false;
    }
  }
}

// 创建全局实例
const mathFormulaUtils = new MathFormulaUtils();

// 导出工具函数
export default mathFormulaUtils;
