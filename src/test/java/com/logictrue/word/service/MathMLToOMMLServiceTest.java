package com.logictrue.word.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;

/**
 * MathML转OMML服务测试类
 */
@ExtendWith(MockitoExtension.class)
class MathMLToOMMLServiceTest {

    @InjectMocks
    private MathMLToOMMLService mathMLToOMMLService;

    @BeforeEach
    void setUp() {
        // 测试前的初始化工作
    }

    @Test
    void testConvertSimpleMathML() {
        // 测试简单的数学公式转换
        String mathML = "<math><mi>x</mi><mo>=</mo><mn>5</mn></math>";
        String omml = mathMLToOMMLService.convertMathMLToOMML(mathML);
        
        assertNotNull(omml, "OMML结果不应为null");
        assertTrue(omml.contains("oMath"), "OMML应包含oMath元素");
        assertTrue(omml.contains("m:r"), "OMML应包含文本运行元素");
    }

    @Test
    void testConvertSuperscriptMathML() {
        // 测试上标公式转换
        String mathML = "<math><msup><mi>x</mi><mn>2</mn></msup></math>";
        String omml = mathMLToOMMLService.convertMathMLToOMML(mathML);
        
        assertNotNull(omml, "OMML结果不应为null");
        assertTrue(omml.contains("m:sSup"), "OMML应包含上标元素");
        assertTrue(omml.contains("m:e"), "OMML应包含基数元素");
        assertTrue(omml.contains("m:sup"), "OMML应包含上标内容元素");
    }

    @Test
    void testConvertFractionMathML() {
        // 测试分数公式转换
        String mathML = "<math><mfrac><mi>a</mi><mi>b</mi></mfrac></math>";
        String omml = mathMLToOMMLService.convertMathMLToOMML(mathML);
        
        assertNotNull(omml, "OMML结果不应为null");
        assertTrue(omml.contains("m:f"), "OMML应包含分数元素");
        assertTrue(omml.contains("m:num"), "OMML应包含分子元素");
        assertTrue(omml.contains("m:den"), "OMML应包含分母元素");
    }

    @Test
    void testConvertSquareRootMathML() {
        // 测试平方根公式转换
        String mathML = "<math><msqrt><mi>x</mi></msqrt></math>";
        String omml = mathMLToOMMLService.convertMathMLToOMML(mathML);
        
        assertNotNull(omml, "OMML结果不应为null");
        assertTrue(omml.contains("m:rad"), "OMML应包含根号元素");
        assertTrue(omml.contains("m:e"), "OMML应包含根号内容元素");
    }

    @Test
    void testConvertComplexMathML() {
        // 测试复杂公式转换
        String mathML = "<math><mfrac><msup><mi>x</mi><mn>2</mn></msup><mi>y</mi></mfrac></math>";
        String omml = mathMLToOMMLService.convertMathMLToOMML(mathML);
        
        assertNotNull(omml, "OMML结果不应为null");
        assertTrue(omml.contains("m:f"), "OMML应包含分数元素");
        assertTrue(omml.contains("m:sSup"), "OMML应包含上标元素");
    }

    @Test
    void testIsValidMathML() {
        // 测试有效的MathML
        String validMathML = "<math><mi>x</mi></math>";
        assertTrue(mathMLToOMMLService.isValidMathML(validMathML), "应识别为有效的MathML");
        
        // 测试包含math标签的字符串
        String mathMLWithNamespace = "<math xmlns='http://www.w3.org/1998/Math/MathML'><mi>x</mi></math>";
        assertTrue(mathMLToOMMLService.isValidMathML(mathMLWithNamespace), "应识别为有效的MathML");
    }

    @Test
    void testIsInvalidMathML() {
        // 测试无效的MathML
        String invalidMathML = "<invalid>test</invalid>";
        assertFalse(mathMLToOMMLService.isValidMathML(invalidMathML), "应识别为无效的MathML");
        
        // 测试空字符串
        assertFalse(mathMLToOMMLService.isValidMathML(""), "空字符串应识别为无效");
        assertFalse(mathMLToOMMLService.isValidMathML(null), "null应识别为无效");
        
        // 测试格式错误的XML
        String malformedXML = "<math><mi>x</mi>";
        assertFalse(mathMLToOMMLService.isValidMathML(malformedXML), "格式错误的XML应识别为无效");
    }

    @Test
    void testConvertEmptyMathML() {
        // 测试空MathML
        String emptyMathML = "";
        String omml = mathMLToOMMLService.convertMathMLToOMML(emptyMathML);
        
        assertEquals("", omml, "空MathML应返回空字符串");
    }

    @Test
    void testConvertNullMathML() {
        // 测试null MathML
        String omml = mathMLToOMMLService.convertMathMLToOMML(null);
        
        assertEquals("", omml, "null MathML应返回空字符串");
    }

    @Test
    void testConvertInvalidMathML() {
        // 测试无效的MathML格式
        String invalidMathML = "<invalid>test</invalid>";
        String omml = mathMLToOMMLService.convertMathMLToOMML(invalidMathML);
        
        assertNotNull(omml, "即使输入无效，也应返回备用OMML");
        assertTrue(omml.contains("公式转换失败") || omml.contains("oMath"), 
                  "应返回错误提示或备用OMML");
    }

    @Test
    void testConvertMalformedXML() {
        // 测试格式错误的XML
        String malformedXML = "<math><mi>x</mi>";
        String omml = mathMLToOMMLService.convertMathMLToOMML(malformedXML);
        
        assertNotNull(omml, "格式错误的XML应返回备用OMML");
        assertTrue(omml.contains("公式转换失败") || omml.contains("oMath"), 
                  "应返回错误提示或备用OMML");
    }

    @Test
    void testConvertMathMLWithNamespace() {
        // 测试带命名空间的MathML
        String mathMLWithNamespace = "<math xmlns='http://www.w3.org/1998/Math/MathML'>" +
                                   "<mi>x</mi><mo>=</mo><mn>5</mn></math>";
        String omml = mathMLToOMMLService.convertMathMLToOMML(mathMLWithNamespace);
        
        assertNotNull(omml, "带命名空间的MathML应能正确转换");
        assertTrue(omml.contains("oMath"), "OMML应包含oMath元素");
    }

    @Test
    void testConvertNestedMathML() {
        // 测试嵌套的MathML结构
        String nestedMathML = "<math><mrow><mi>x</mi><mo>+</mo><mrow><mi>y</mi><mo>*</mo><mi>z</mi></mrow></mrow></math>";
        String omml = mathMLToOMMLService.convertMathMLToOMML(nestedMathML);
        
        assertNotNull(omml, "嵌套MathML应能正确转换");
        assertTrue(omml.contains("oMath"), "OMML应包含oMath元素");
    }

    @Test
    void testConvertSubscriptMathML() {
        // 测试下标公式转换
        String mathML = "<math><msub><mi>x</mi><mn>1</mn></msub></math>";
        String omml = mathMLToOMMLService.convertMathMLToOMML(mathML);
        
        assertNotNull(omml, "OMML结果不应为null");
        assertTrue(omml.contains("m:sSub"), "OMML应包含下标元素");
        assertTrue(omml.contains("m:e"), "OMML应包含基数元素");
        assertTrue(omml.contains("m:sub"), "OMML应包含下标内容元素");
    }

    @Test
    void testConvertNthRootMathML() {
        // 测试n次根公式转换
        String mathML = "<math><mroot><mi>x</mi><mn>3</mn></mroot></math>";
        String omml = mathMLToOMMLService.convertMathMLToOMML(mathML);
        
        assertNotNull(omml, "OMML结果不应为null");
        assertTrue(omml.contains("m:rad"), "OMML应包含根号元素");
        assertTrue(omml.contains("m:deg"), "OMML应包含根指数元素");
        assertTrue(omml.contains("m:e"), "OMML应包含根号内容元素");
    }
}
