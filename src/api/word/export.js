import request from '@/utils/request'

/**
 * Word导出相关API
 */

/**
 * 导出表格到Word文档
 * @param {Object} data 表格数据
 * @returns {Promise} 返回文件流
 */
export function exportTableToWord(data) {
  return request({
    url: '/word/wordExport/exportTable',
    method: 'post',
    data: data,
    responseType: 'blob', // 重要：设置响应类型为blob以处理文件流
    timeout: 60000 // 设置60秒超时，因为Word导出可能需要较长时间
  })
}

/**
 * 检查Word导出服务健康状态
 * @returns {Promise}
 */
export function checkWordExportHealth() {
  return request({
    url: '/word/wordExport/health',
    method: 'get'
  })
}
